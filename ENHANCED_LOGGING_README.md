# Enhanced Logging and State Management for Black Build Agents

This document describes the comprehensive logging and state management enhancements added to all agents in the black-build-agents project, following Google ADK best practices.

## Overview

The enhanced logging and state management system provides:

- **Structured Logging**: Consistent, searchable log format across all agents
- **State Tracking**: Persistent state management using ADK session state
- **Performance Monitoring**: Execution time tracking and performance metrics
- **Error Tracking**: Comprehensive error logging and counting
- **Observability**: Deep insights into agent behavior through callbacks

## Architecture

### Core Components

1. **AgentLogger**: Centralized logging utility with structured output
2. **BaseAgentState**: Base class for agent state management
3. **Standard Callbacks**: Reusable ADK callbacks for logging and state tracking
4. **Agent-Specific State Classes**: Specialized state management for each agent type

### File Structure

```
agents/
├── shared/
│   ├── __init__.py
│   └── logging_utils.py          # Shared logging utilities
├── database_agent/
│   └── agent.py                  # Enhanced with logging & state
├── github_agent/
│   └── agent.py                  # Enhanced with logging & state
└── sequential_agent/
    └── agent.py                  # Enhanced with logging & state
```

## Features

### 1. Structured Logging

All agents now use structured logging with consistent format:

```
2024-01-15 10:30:45 - database_agent - INFO - Agent execution started | agent_name=database_agent | session_id=session_123 | user_message_length=45
```

**Log Levels:**
- `DEBUG`: Detailed execution information (LLM calls, tool discovery)
- `INFO`: General execution flow (agent start/stop, tool execution)
- `WARNING`: Non-critical issues (missing tools, configuration warnings)
- `ERROR`: Critical failures (connection errors, tool failures)

### 2. State Management

Each agent maintains comprehensive state information:

**Database Agent State:**
- Connection status to PostgreSQL
- Number of tools discovered
- Query execution count
- Error tracking
- Analysis progress

**GitHub Agent State:**
- API connection status
- Rate limiting information
- Repositories analyzed
- PR analysis progress
- Commit tracking status

**Sequential Agent State:**
- Pipeline execution status
- Sub-agent completion tracking
- Data flow between agents
- Overall execution metrics

### 3. ADK Callbacks

All agents implement comprehensive ADK callbacks:

**Before Agent Callback:**
- Logs execution start
- Records start timestamp
- Updates session state

**After Agent Callback:**
- Logs execution completion
- Calculates execution time
- Updates completion metrics

**Before/After Model Callbacks:**
- Tracks LLM interactions
- Monitors model performance
- Logs request/response sizes

**Before/After Tool Callbacks:**
- Monitors tool execution
- Tracks success/failure rates
- Records tool performance

### 4. Session State Integration

State is persisted in ADK session state with prefixed keys:

```python
# Agent-specific state
session.state["db_agent:execution_time"] = 2.34
session.state["github_agent:api_calls_made"] = 15
session.state["sequential_agent:pipeline_status"] = "running"

# User-level state (persistent across sessions)
session.state["user:preferred_analysis_depth"] = "detailed"

# Application-level state (shared across users)
session.state["app:rate_limit_config"] = {...}
```

## Usage Examples

### Using Shared Logging Utilities

```python
from agents.shared import AgentLogger, BaseAgentState, create_standard_callbacks

# Create logger and state
logger = AgentLogger("my_agent", "my_agent.log")
state = BaseAgentState()

# Create standard callbacks
callbacks = create_standard_callbacks(logger, state)

# Use in agent creation
agent = Agent(
    name="my_agent",
    **callbacks,  # Adds all standard callbacks
    # ... other agent parameters
)
```

### Custom State Management

```python
class MyAgentState(BaseAgentState):
    def __init__(self):
        super().__init__()
        self.custom_metric = 0
        self.processing_queue = []
    
    def to_dict(self):
        base_dict = super().to_dict()
        base_dict.update({
            "custom_metric": self.custom_metric,
            "processing_queue": self.processing_queue
        })
        return base_dict
```

## Log Files

Each agent creates its own log file:

- `database_agent.log`: Database agent logs
- `github_agent.log`: GitHub agent logs  
- `sequential_agent.log`: Sequential agent pipeline logs

## Monitoring and Observability

### Key Metrics Tracked

1. **Execution Metrics:**
   - Agent execution time
   - Tool execution time
   - LLM call frequency and duration

2. **Error Metrics:**
   - Error count per agent
   - Error types and frequencies
   - Tool failure rates

3. **Performance Metrics:**
   - API call rates (GitHub)
   - Query execution rates (Database)
   - Pipeline completion times (Sequential)

4. **State Metrics:**
   - Connection status
   - Resource utilization
   - Progress tracking

### Sample Log Analysis

```bash
# Find all errors across agents
grep "ERROR" *.log

# Track execution times
grep "execution completed" *.log | grep -o "execution_time_seconds=[0-9.]*"

# Monitor API usage
grep "api_calls_made" sequential_agent.log

# Check connection status
grep "connection_status" *.log
```

## Best Practices

### 1. Log Message Format

- Use structured logging with key=value pairs
- Include relevant context (agent_name, session_id, etc.)
- Keep messages concise but informative

### 2. State Management

- Update state consistently through callbacks
- Use appropriate state prefixes (agent:, user:, app:)
- Serialize complex objects properly

### 3. Error Handling

- Log errors with full context
- Track error counts for monitoring
- Provide actionable error messages

### 4. Performance

- Log execution times for optimization
- Track resource usage patterns
- Monitor for performance degradation

## Configuration

### Environment Variables

```bash
# Logging configuration
LOG_LEVEL=INFO
LOG_FORMAT="%(asctime)s - %(name)s - %(levelname)s - %(message)s"

# Agent-specific configuration
DATABASE_URL=postgresql://...
GITHUB_PERSONAL_ACCESS_TOKEN=...
JENKINS_URL=...
```

### Customization

Agents can be customized by:

1. Extending the base state classes
2. Adding custom callback logic
3. Configuring specific log levels
4. Adding custom metrics tracking

## Troubleshooting

### Common Issues

1. **Missing Log Files**: Check file permissions and disk space
2. **State Not Persisting**: Verify session service configuration
3. **High Log Volume**: Adjust log levels or add filtering
4. **Performance Impact**: Monitor callback execution overhead

### Debug Mode

Enable debug logging for detailed execution traces:

```python
logger.setLevel(logging.DEBUG)
```

This provides detailed information about:
- LLM request/response content
- Tool argument details
- State change tracking
- Internal execution flow

## Future Enhancements

Planned improvements include:

1. **Metrics Dashboard**: Web-based monitoring interface
2. **Alert System**: Automated error notifications
3. **Performance Analytics**: Trend analysis and optimization
4. **Log Aggregation**: Centralized log collection and search
5. **State Visualization**: Graphical state inspection tools
