import asyncio
import json
import os
import sys
import traceback
import re
from typing import Dict, List, Optional, Any, Union, AsyncIterator
from datetime import datetime, timedelta
from contextlib import asynccontextmanager
import requests
from mcp.server.fastmcp import FastMC<PERSON>, Context
from dotenv import load_dotenv
import time
import threading
from concurrent.futures import ThreadPoolExecutor

load_dotenv()

# GitHub API configuration
GITHUB_API_BASE = "https://api.github.com"
GITHUB_API_VERSION = "2022-11-28"  # Using a stable API version

# Cache for API responses
_cache = {}
_cache_lock = threading.Lock()

def cache_key(*args):
    """Generate cache key from arguments"""
    return "|".join(str(arg) for arg in args)

def get_cached(key):
    """Get cached response"""
    with _cache_lock:
        return _cache.get(key)

def set_cache(key, value):
    """Set cached response"""
    with _cache_lock:
        _cache[key] = value

def debug_log(message, level="INFO"):
    """Write log messages to stderr with severity level
    
    Args:
        message: The message to log
        level: Severity level (INFO, WARNING, ERROR)
    """
    level = level.upper()
    print(f"GITHUB-MCP [{level}]: {message}", file=sys.stderr)

def log_exception(e, context=""):
    """Log exception with traceback
    
    Args:
        e: The exception
        context: Additional context about where the exception occurred
    """
    error_msg = f"{context}: {str(e)}" if context else str(e)
    debug_log(error_msg, "ERROR")
    debug_log(traceback.format_exc(), "ERROR")

class GitHubAPI:
    def __init__(self, token: str):
        self.token = token
        self.session = requests.Session()
        self.session.headers.update({
            "Authorization": f"token {token}",
            "Accept": "application/vnd.github.v3+json",
            "X-GitHub-Api-Version": GITHUB_API_VERSION
        })
    
    def _make_request(self, method: str, endpoint: str, **kwargs) -> Dict:
        """Make a request to the GitHub API with proper error handling, caching, and rate limiting
        
        Args:
            method: HTTP method (GET, POST, etc.)
            endpoint: API endpoint (without base URL)
            **kwargs: Additional arguments to pass to requests
            
        Returns:
            Dict containing the JSON response
            
        Raises:
            requests.exceptions.RequestException: If the request fails
        """
        url = f"{GITHUB_API_BASE}{endpoint}"
        
        # Check cache first for GET requests
        if method.upper() == "GET":
            cache_k = cache_key(url, str(kwargs.get('params', {})))
            cached = get_cached(cache_k)
            if cached:
                return cached
        
        try:
            response = self.session.request(method, url, **kwargs)
            
            # Handle rate limiting
            if response.status_code == 403 and 'X-RateLimit-Remaining' in response.headers and int(response.headers['X-RateLimit-Remaining']) == 0:
                reset_time = int(response.headers['X-RateLimit-Reset'])
                sleep_time = max(1, reset_time - int(time.time()))
                debug_log(f"Rate limit hit. Waiting {sleep_time} seconds...", "WARNING")
                time.sleep(sleep_time)
                return self._make_request(method, endpoint, **kwargs)
            
            response.raise_for_status()
            result = response.json()
            
            # Cache the result for GET requests
            if method.upper() == "GET":
                set_cache(cache_k, result)
            
            return result
            
        except requests.exceptions.RequestException as e:
            if response.status_code == 404:
                raise ValueError(f"Resource not found: {endpoint}")
            elif response.status_code == 403:
                raise ValueError("API rate limit exceeded or insufficient permissions")
            else:
                raise ValueError(f"GitHub API error: {str(e)}")
    
    def make_concurrent_requests(self, requests_data):
        """Make multiple requests concurrently"""
        results = {}
        
        def fetch_single(request_info):
            endpoint, params, key = request_info
            result = self._make_request("GET", endpoint, params=params)
            return key, result
        
        with ThreadPoolExecutor(max_workers=10) as executor:
            futures = [executor.submit(fetch_single, req) for req in requests_data]
            for future in futures:
                try:
                    key, result = future.result(timeout=30)
                    results[key] = result
                except Exception as e:
                    debug_log(f"Concurrent request failed: {e}", "ERROR")
        
        return results
    
    def get_repo(self, repo_name: str) -> Dict:
        """Get repository information"""
        return self._make_request("GET", f"/repos/{repo_name}")
    
    def get_pull_request(self, repo_name: str, pr_number: int) -> Dict:
        """Get pull request information"""
        return self._make_request("GET", f"/repos/{repo_name}/pulls/{pr_number}")
    
    def get_pull_request_commits(self, repo_name: str, pr_number: int) -> List[Dict]:
        """Get commits for a pull request"""
        return self._make_request("GET", f"/repos/{repo_name}/pulls/{pr_number}/commits")
    
    def get_pull_requests(self, repo_name: str, state: str = "open", per_page: int = 10) -> List[Dict]:
        """List pull requests in a repository"""
        return self._make_request("GET", f"/repos/{repo_name}/pulls", params={
            "state": state,
            "per_page": per_page
        })
    
    def get_commit(self, repo_name: str, commit_sha: str) -> Dict:
        """Get commit information"""
        return self._make_request("GET", f"/repos/{repo_name}/commits/{commit_sha}")
    
    def get_pull_request_reviews(self, repo_name: str, pr_number: int) -> List[Dict]:
        """Get reviews for a pull request"""
        return self._make_request("GET", f"/repos/{repo_name}/pulls/{pr_number}/reviews")
    
    def get_branches(self, repo_name: str, per_page: int = 50) -> List[Dict]:
        """Get all branches in a repository"""
        return self._make_request("GET", f"/repos/{repo_name}/branches", params={"per_page": per_page})
    
    def get_branch(self, repo_name: str, branch_name: str) -> Dict:
        """Get information about a specific branch"""
        return self._make_request("GET", f"/repos/{repo_name}/branches/{branch_name}")

debug_log("Starting GitHub MCP server...")

@asynccontextmanager
async def app_lifespan(server: FastMCP) -> AsyncIterator[GitHubAPI]:
    """Manage GitHub API client lifecycle"""
    debug_log("Starting GitHub API client lifespan")
    github_client = None
    
    try:
        # Get GitHub token from environment variable
        github_token = os.getenv("GITHUB_PERSONAL_ACCESS_TOKEN")
        if not github_token:
            debug_log("GITHUB_PERSONAL_ACCESS_TOKEN environment variable not set", "WARNING")
            github_token = os.getenv("GITHUB_TOKEN")
            if not github_token:
                debug_log("GITHUB_TOKEN environment variable not set", "ERROR")
                raise ValueError("GitHub token not found in environment variables")
        
        debug_log("Initializing GitHub API client")
        github_client = GitHubAPI(github_token)
        debug_log("GitHub API client initialized successfully")
        
        yield github_client
    except Exception as e:
        log_exception(e, "Error in GitHub API client lifespan")
        raise
    finally:
        if github_client:
            debug_log("Closing GitHub API client")
            github_client.session.close()
        debug_log("Exiting GitHub API client lifespan")

# Initialize the MCP server
mcp = FastMCP("GitHub Pull Request Analyzer", lifespan=app_lifespan, request_timeout=300)

@mcp.tool()
async def find_pr_commits(ctx: Context, repo_name: str, pr_number: int) -> str:
    """
    Find commits for a specific pull request with their timestamps and authors,
    along with base and head branch information.
    
    Args:
        repo_name: The full name of the repository (e.g., 'owner/repo')
        pr_number: The pull request number
        
    Returns:
        A formatted JSON string with pull request details including base SHA, head SHA,
        head branch name, and a list of commit details (SHA, message, timestamp, author).
    """
    debug_log(f"Finding commits for PR #{pr_number} in {repo_name}")
    try:
        # Get GitHub client from context
        gh = ctx.request_context.lifespan_context
        
        # Get pull request and commits
        pr = gh.get_pull_request(repo_name, pr_number)
        commits = gh.get_pull_request_commits(repo_name, pr_number)
        
        # Format commit information
        formatted_commits = []
        for commit in commits:
            commit_info = {
                "sha": commit["sha"],
                "message": commit["commit"]["message"],
                "timestamp": commit["commit"]["author"]["date"],
                "author": {
                    "name": commit["commit"]["author"]["name"],
                    "email": commit["commit"]["author"]["email"]
                }
            }
            # Add GitHub username if available
            if commit.get("author"):
                commit_info["author"]["login"] = commit["author"]["login"]
                
            formatted_commits.append(commit_info)
        
        response_data = {
            "pull_request_number": pr_number,
            "repository_name": repo_name,
            "base_branch_sha": pr["base"]["sha"],
            "base_branch_name": pr["base"]["ref"],
            "head_branch_sha": pr["head"]["sha"],
            "head_branch_name": pr["head"]["ref"],
            "pr_status": pr["state"],
            "merge_type": pr.get("merge_commit_sha") and "merge" or None,
            "commits": formatted_commits
        }
        
        debug_log(f"Successfully processed commits for PR #{pr_number}")
        return json.dumps(response_data, indent=2)
    
    except ValueError as e:
        log_exception(e, f"GitHub API error for PR #{pr_number}")
        return json.dumps({"error": str(e)})
    except Exception as e:
        log_exception(e, f"Error processing commits for PR #{pr_number}")
        return json.dumps({"error": f"An error occurred: {str(e)}"})

@mcp.tool()
async def list_repo_pull_requests(ctx: Context, repo_name: str, state: str = "open", limit: int = 10) -> str:
    """
    List pull requests in a GitHub repository.
    
    Args:
        repo_name: The full name of the repository (e.g., 'owner/repo')
        state: The state of the pull requests to list ('open', 'closed', or 'all')
        limit: The maximum number of pull requests to return (default: 10)
        
    Returns:
        A formatted JSON string with pull request details
    """
    debug_log(f"Listing {state} PRs for {repo_name}, limit: {limit}")
    try:
        # Get GitHub client from context
        gh = ctx.request_context.lifespan_context
        
        # Get pull requests
        pull_requests = gh.get_pull_requests(repo_name, state=state, per_page=limit)
        debug_log(f"Found {len(pull_requests)} {state} PRs for {repo_name}")
        
        # Format pull request information
        formatted_prs = []
        for pr in pull_requests:
            pr_info = {
                "number": pr["number"],
                "title": pr["title"],
                "state": pr["state"],
                "created_at": pr["created_at"],
                "updated_at": pr["updated_at"],
                "user": pr["user"]["login"] if pr["user"] else "Unknown",
                "url": pr["html_url"]
            }
            formatted_prs.append(pr_info)
        
        debug_log(f"Successfully processed {len(formatted_prs)} PRs")
        return json.dumps(formatted_prs, indent=2)
    
    except ValueError as e:
        log_exception(e, f"GitHub API error listing PRs for {repo_name}")
        return json.dumps({"error": str(e)})
    except Exception as e:
        log_exception(e, f"Error listing PRs for {repo_name}")
        return json.dumps({"error": f"An error occurred: {str(e)}"})

@mcp.tool()
async def get_commit_details(ctx: Context, repo_name: str, commit_sha: str) -> str:
    """
    Get detailed information about a specific commit.
    
    Args:
        repo_name: The full name of the repository (e.g., 'owner/repo')
        commit_sha: The SHA of the commit
        
    Returns:
        A formatted JSON string with commit details
    """
    debug_log(f"Getting details for commit {commit_sha} in {repo_name}")
    try:
        # Get GitHub client from context
        gh = ctx.request_context.lifespan_context
        
        # Get commit
        commit = gh.get_commit(repo_name, commit_sha)
        
        # Format commit information
        commit_info = {
            "sha": commit["sha"],
            "message": commit["commit"]["message"],
            "timestamp": commit["commit"]["author"]["date"],
            "author": {
                "name": commit["commit"]["author"]["name"],
                "email": commit["commit"]["author"]["email"]
            },
            "stats": commit["stats"],
            "files": [
                {
                    "filename": file["filename"],
                    "status": file["status"],
                    "additions": file["additions"],
                    "deletions": file["deletions"],
                    "changes": file["changes"]
                }
                for file in commit["files"]
            ]
        }
        
        # Add GitHub username if available
        if commit.get("author"):
            commit_info["author"]["login"] = commit["author"]["login"]
        
        debug_log(f"Successfully retrieved details for commit {commit_sha}")
        return json.dumps(commit_info, indent=2)
    
    except ValueError as e:
        log_exception(e, f"GitHub API error for commit {commit_sha}")
        return json.dumps({"error": str(e)})
    except Exception as e:
        log_exception(e, f"Error processing details for commit {commit_sha}")
        return json.dumps({"error": f"An error occurred: {str(e)}"})
 

@mcp.tool()
async def trace_commit_path(ctx: Context, repo_name: str, commit_hash: str, stop_at_branch: str = None, auto_discover: bool = True, branches_to_check: str = None) -> str:
    """
    Trace a commit's path through branches in a GitHub repository to understand its promotion history
    with improved performance and auto-discovery capabilities.
    
    Args:
        repo_name: The full name of the repository (e.g., 'owner/repo')
        commit_hash: The SHA of the commit to trace
        stop_at_branch: Optional name of a branch to stop tracing at (e.g., 'staging')
        auto_discover: Whether to auto-discover important branches (default: True)
        branches_to_check: Comma-separated list of specific branches to check (overrides auto-discovery)
        
    Returns:
        A formatted JSON string with commit tracing information, including:
        - Which branches contain the commit
        - Pull requests that included the commit
        - The promotion path from branch to branch
        - Execution time and performance metrics
    """
    debug_log(f"Fast tracing path for commit {commit_hash} in {repo_name}")
    start_time = time.time()
    
    try:
        # Parse owner and repo from repo_name
        parts = repo_name.split('/')
        if len(parts) != 2:
            return json.dumps({"error": "Invalid repository name format. Expected 'owner/repo'"})
            
        owner, repo = parts
        
        # Get GitHub client from context
        gh = ctx.request_context.lifespan_context
        if not gh:
            return json.dumps({"error": "GitHub client not available"})
        
        result = {
            "status": "success",
            "commit_hash": commit_hash,
            "owner": owner,
            "repo": repo,
            "formatted_output": [],
            "execution_time": 0
        }
        
        # Determine branches to check
        branches_list = None
        if branches_to_check:
            branches_list = [b.strip() for b in branches_to_check.split(',')]
            debug_log(f"Using provided branches: {', '.join(branches_list)}")
        elif auto_discover:
            # Auto-discover branches
            debug_log("Auto-discovering repository branches...")
            all_branches = gh.get_branches(repo_name, per_page=100)
            if not all_branches:
                return json.dumps({
                    "status": "error",
                    "message": "Could not discover repository branches"
                })
            
            branch_names = [branch["name"] for branch in all_branches]
            branches_list = suggest_important_branches(branch_names)
            debug_log(f"Auto-selected branches: {', '.join(branches_list)}")
            result["auto_discovered_branches"] = branch_names
            result["selected_branches"] = branches_list
        else:
            # Use default branches
            branches_list = ['develop', 'UAT', 'staging', 'main']
        
        if stop_at_branch and stop_at_branch in branches_list:
            stop_index = branches_list.index(stop_at_branch)
            branches_list = branches_list[:stop_index + 1]
            debug_log(f"Stopping at branch {stop_at_branch}, checking: {', '.join(branches_list)}")
        
        # Check if this commit is directly from main (quick check)
        debug_log(f"Checking if commit {commit_hash} is from main branch...")
        is_from_main = await is_commit_from_branch_fast(gh, owner, repo, commit_hash, "main")
        
        if is_from_main:
            message = f"The commit {commit_hash} is from the MAIN branch only. No further traceability needed."
            debug_log(f"\n✅ {message}")
            result["is_from_main"] = True
            result["message"] = message
            result["formatted_output"] = [f"✅ {message}"]
            result["execution_time"] = time.time() - start_time
            return json.dumps(result, indent=2)
        
        # Get commit details and analyze origin
        debug_log("Getting commit details and analyzing origin...")
        commit_data = gh.get_commit(repo_name, commit_hash)
        if not commit_data:
            return json.dumps({
                "status": "error",
                "message": f"Failed to get commit details for {commit_hash}"
            })
        
        origin_info = analyze_commit_origin_fast(commit_data)
        result["commit_info"] = {
            "author": origin_info.get('author', 'Unknown'),
            "date": origin_info.get('date', 'Unknown'),
            "message": origin_info.get('commit_message', ''),
            "is_merge": origin_info.get('is_merge', False),
            "pr_number": origin_info.get('pr_number'),
            "source_branch": origin_info.get('source_branch')
        }
        
        # Check branches containing the commit (concurrent)
        debug_log(f"Checking if commit exists in {len(branches_list)} branches concurrently...")
        branch_status = await check_branches_contain_commit_fast(gh, repo_name, commit_hash, branches_list)
        result["branch_status"] = branch_status
        
        # Find PRs containing this commit using search API
        debug_log("Finding related pull requests using search API...")
        prs = await find_prs_containing_commit_fast(gh, owner, repo, commit_hash)
        result["pull_requests"] = prs
        
        # Build formatted output
        result["formatted_output"] = build_formatted_output(
            commit_hash, origin_info, branch_status, prs, branches_list
        )
        
        result["execution_time"] = time.time() - start_time
        debug_log(f"Fast trace completed in {result['execution_time']:.2f} seconds")
        
        return json.dumps(result, indent=2)
        
    except ValueError as e:
        log_exception(e, f"GitHub API error tracing commit {commit_hash}")
        return json.dumps({"error": str(e)})
    except Exception as e:
        log_exception(e, f"Error tracing commit {commit_hash}")
        return json.dumps({"error": f"An error occurred: {str(e)}"})

def suggest_important_branches(all_branches):
    """Suggest important branches based on common naming patterns"""
    important_patterns = [
        # Production/Main branches
        r'^(main|master)$',
        # Development branches  
        r'^(dev|develop|development)$',
        # Staging/Testing branches
        r'^(staging|stage|uat|test|testing)$',
        # Release branches
        r'^(release|prod|production)$',
        # Integration branches
        r'^(integration|int)$',
        # Feature branches (common ones)
        r'^(feature|feat)$',
        # Hotfix branches
        r'^(hotfix|fix)$'
    ]
    
    suggested = []
    patterns_found = set()
    
    for branch in all_branches:
        for pattern in important_patterns:
            if re.match(pattern, branch, re.IGNORECASE):
                if pattern not in patterns_found:  # Avoid duplicates from same pattern
                    suggested.append(branch)
                    patterns_found.add(pattern)
                break
    
    # Also suggest branches with specific keywords
    keyword_branches = []
    keywords = ['main', 'master', 'dev', 'prod', 'stage', 'release']
    
    for branch in all_branches:
        branch_lower = branch.lower()
        for keyword in keywords:
            if keyword in branch_lower and branch not in suggested:
                keyword_branches.append(branch)
                break
    
    # Combine and limit suggestions
    all_suggested = suggested + keyword_branches
    
    # Remove duplicates while preserving order
    seen = set()
    unique_suggested = []
    for branch in all_suggested:
        if branch not in seen:
            unique_suggested.append(branch)
            seen.add(branch)
    
    return unique_suggested[:8]  # Limit to 8 suggestions

async def is_commit_from_branch_fast(gh, owner, repo, commit_hash, branch_name="main"):
    """Fast check if a commit originated directly in a specific branch"""
    repo_name = f"{owner}/{repo}"
    
    try:
        # Get the branch commit data
        branch_data = gh.get_branch(repo_name, branch_name)
        if not branch_data:
            return False
            
        # If the commit is not in the branch, it's not from this branch
        comparison = gh._make_request("GET", f"/repos/{repo_name}/compare/{commit_hash}...{branch_name}")
        if not comparison:
            return False
            
        if comparison.get("status") not in ["identical", "behind"]:
            debug_log(f"\nCommit is not in {branch_name} branch at all.")
            return False
        
        # Check if it's a merge commit (which likely didn't originate in this branch)
        commit_data = gh.get_commit(repo_name, commit_hash)
        if not commit_data:
            return False
            
        origin_info = analyze_commit_origin_fast(commit_data)
        if not origin_info:
            return False
            
        if origin_info["is_merge"]:
            debug_log(f"\nCommit is in {branch_name} but is a merge commit.")
            if origin_info["source_branch"]:
                debug_log(f"   Likely originated from: {origin_info['source_branch']}")
            if origin_info["pr_number"]:
                debug_log(f"   Merged via PR #{origin_info['pr_number']}")
            return False
        
        # Check if the commit was part of a PR using search
        search_url = "https://api.github.com/search/issues"
        params = {
            "q": f"type:pr repo:{owner}/{repo} {commit_hash}",
        }
        
        response = requests.get(search_url, params=params, headers=gh.session.headers)
        if response.status_code == 200:
            pr_search = response.json()
            # If the commit appears in PRs, it likely didn't originate directly in this branch
            if pr_search.get("total_count", 0) > 0:
                debug_log(f"\nCommit is in {branch_name} but was introduced through a PR.")
                return False
        
        return True
    except Exception as e:
        debug_log(f"Error in is_commit_from_branch_fast: {str(e)}")
        return False

def analyze_commit_origin_fast(commit_data):
    """Fast analysis of commit origin from commit data"""
    try:
        # Check if it's a merge commit (has multiple parents)
        parents = commit_data.get("parents", [])
        is_merge = len(parents) > 1
        
        # Get commit message
        commit_msg = commit_data.get("commit", {}).get("message", "")
        
        # Check for PR information
        pr_match = re.search(r"Merge pull request #(\d+)", commit_msg)
        pr_number = pr_match.group(1) if pr_match else None
        
        # Check for source branch information
        branch_match = re.search(r"from\s+(\S+)", commit_msg)
        source_branch = branch_match.group(1) if branch_match else None
        
        return {
            "is_merge": is_merge,
            "source_branch": source_branch,
            "pr_number": pr_number,
            "commit_message": commit_msg,
            "parent_count": len(parents),
            "parents": [p.get("sha") for p in parents],
            "author": commit_data.get("commit", {}).get("author", {}).get("name"),
            "date": commit_data.get("commit", {}).get("author", {}).get("date")
        }
    except Exception as e:
        debug_log(f"Error in analyze_commit_origin_fast: {str(e)}")
        return {}

async def check_branches_contain_commit_fast(gh, repo_name, commit_hash, branches_to_check):
    """Check multiple branches at once using concurrent requests"""
    if not branches_to_check:
        return {}
        
    debug_log(f"Checking if commit exists in {len(branches_to_check)} branches concurrently...")
    
    # Prepare concurrent requests
    requests_to_make = []
    for branch in branches_to_check:
        endpoint = f"/repos/{repo_name}/compare/{commit_hash}...{branch}"
        requests_to_make.append((endpoint, {}, branch))
    
    # Make all requests concurrently
    results = gh.make_concurrent_requests(requests_to_make)
    
    branch_status = {}
    for branch in branches_to_check:
        comparison = results.get(branch)
        if comparison:
            status = comparison.get("status")
            # Handle different comparison statuses
            if status == "identical":
                contains = True
            elif status == "behind":
                contains = True
            elif status == "ahead":
                contains = False
            elif status == "diverged":
                contains = comparison.get("behind_by", 0) > 0
            else:
                contains = False
                
            branch_status[branch] = {
                "contains": contains,
                "status": status,
                "ahead_by": comparison.get("ahead_by", 0),
                "behind_by": comparison.get("behind_by", 0)
            }
        else:
            branch_status[branch] = {"contains": False, "error": True}
    
    return branch_status

async def find_prs_containing_commit_fast(gh, owner, repo, commit_hash):
    """Find PRs containing commit using search API (faster than checking each PR)"""
    search_url = "https://api.github.com/search/issues"
    params = {
        "q": f"type:pr repo:{owner}/{repo} {commit_hash}",
        "per_page": 100
    }
    
    try:
        response = requests.get(search_url, params=params, headers=gh.session.headers)
        if response.status_code != 200:
            debug_log(f"PR search failed: {response.status_code} - {response.text}")
            return []
            
        pr_search = response.json()
        
        prs = []
        for item in pr_search.get("items", []):
            # Get PR details to check if it's merged
            try:
                pr_data = gh.get_pull_request(f"{owner}/{repo}", item['number'])
                merged_at = pr_data.get("merged_at") if pr_data else None
                base_branch = pr_data.get("base", {}).get("ref") if pr_data else "unknown"
                head_branch = pr_data.get("head", {}).get("ref") if pr_data else "unknown"
            except:
                merged_at = None
                base_branch = "unknown"
                head_branch = "unknown"
                
            prs.append({
                "number": item["number"],
                "title": item["title"],
                "url": item["html_url"],
                "state": item["state"],
                "merged_at": merged_at,
                "created_at": item.get("created_at"),
                "user": item.get("user", {}).get("login"),
                "base_branch": base_branch,
                "head_branch": head_branch
            })
        
        return prs
    except Exception as e:
        debug_log(f"Error in find_prs_containing_commit_fast: {str(e)}")
        return []

def build_formatted_output(commit_hash, origin_info, branch_status, prs, branches_checked):
    """Build comprehensive formatted output for the trace results"""
    output = []
    
    # Commit info
    output.append(f"🔹 Commit: {commit_hash}")
    output.append(f"🔹 Author: {origin_info.get('author', 'Unknown')}")
    output.append(f"🔹 Date: {origin_info.get('date', 'Unknown')}")
    output.append(f"🔹 Message: {origin_info.get('commit_message', '')[:100]}...")
    
    if origin_info.get("is_merge"):
        output.append(f"🔹 Type: Merge commit ({origin_info.get('parent_count')} parents)")
        if origin_info.get("pr_number"):
            output.append(f"🔹 Merged PR: #{origin_info.get('pr_number')}")
        if origin_info.get("source_branch"):
            output.append(f"🔹 Source branch: {origin_info.get('source_branch')}")
    else:
        output.append(f"🔹 Type: Regular commit")
    
    # Branch status
    output.append(f"\n📈 BRANCH STATUS:")
    branches_with_commit = []
    for branch in branches_checked:
        status = branch_status.get(branch, {})
        if status.get("contains", False):
            output.append(f"  ✅ {branch.upper()}: Contains commit")
            branches_with_commit.append(branch)
            if status.get("behind_by", 0) > 0:
                output.append(f"     ({status['behind_by']} commits behind)")
        elif status.get("error"):
            output.append(f"  ❓ {branch.upper()}: Could not check (may not exist or be accessible)")
        else:
            output.append(f"  ❌ {branch.upper()}: Does not contain commit")
            if status.get("ahead_by", 0) > 0:
                output.append(f"     (branch is {status['ahead_by']} commits ahead)")
    
    # Pull requests
    if prs:
        output.append(f"\n🚀 PULL REQUESTS ({len(prs)} found):")
        for pr in prs:
            output.append(f"  📋 PR #{pr['number']}: {pr['title']}")
            output.append(f"     🌿 {pr.get('head_branch', 'unknown')} → {pr.get('base_branch', 'unknown')}")
            output.append(f"     👤 by {pr.get('user', 'unknown')}")
            output.append(f"     📅 {pr.get('created_at', 'unknown date')}")
            if pr.get("merged_at"):
                output.append(f"     ✅ Merged: {pr.get('merged_at')}")
            output.append(f"     🔗 {pr['url']}")
            output.append("")
    else:
        output.append(f"\n🚀 PULL REQUESTS: None found")
    
    # Quick analysis
    output.append(f"\n🧠 QUICK ANALYSIS:")
    if not branches_with_commit:
        output.append("  ⚠️  Commit not found in any checked branches - may have been removed or is on a different branch")
    elif origin_info.get("is_merge") and origin_info.get("pr_number"):
        output.append(f"  ✅ This is a merge commit from PR #{origin_info.get('pr_number')}")
        output.append(f"     The changes originated from branch: {origin_info.get('source_branch', 'unknown')}")
    elif len(prs) > 0:
        output.append(f"  ✅ This commit was introduced through {len(prs)} pull request(s)")
    else:
        output.append(f"  ℹ️  This appears to be a direct commit (not from a PR)")
    
    return output

# Helper functions for get_commit_graph tool

async def _fetch_branches_graphql(owner: str, repo: str, graphql_url: str, 
                                 graphql_headers: Dict[str, str]) -> Dict[str, str]:
    """Fetch branches using GraphQL API"""
    branches = {}
    debug_log(f"Fetching branches for {owner}/{repo} using GraphQL")
    query = """
    query ($owner: String!, $repo: String!, $cursor: String) {
      repository(owner: $owner, name: $repo) {
        refs(first: 100, refPrefix: "refs/heads/", after: $cursor) {
          pageInfo {
            hasNextPage
            endCursor
          }
          nodes {
            name
            target {
              ... on Commit {
                oid
              }
            }
          }
        }
      }
    }
    """
    
    variables = {
        "owner": owner,
        "repo": repo,
        "cursor": None
    }
    
    has_next_page = True
    total_branches = 0
    
    while has_next_page:
        try:
            debug_log(f"Making GraphQL request for branches, cursor: {variables['cursor']}")
            response = requests.post(
                graphql_url,
                headers=graphql_headers,
                json={"query": query, "variables": variables}
            )
            response.raise_for_status()
            result = response.json()
            
            if "errors" in result:
                debug_log(f"GraphQL Error: {result['errors']}", "ERROR")
                break
            
            data = result.get("data", {}).get("repository", {}).get("refs", {})
            if not data:
                debug_log("No repository or refs data returned from GraphQL", "ERROR")
                break
                
            page_info = data.get("pageInfo", {})
            branch_nodes = data.get("nodes", [])
            
            branch_count = len(branch_nodes)
            debug_log(f"Retrieved {branch_count} branches in this page")
            
            for branch in branch_nodes:
                if branch.get("name") and branch.get("target", {}).get("oid"):
                    branches[branch["name"]] = branch["target"]["oid"]
                else:
                    debug_log(f"Skipped invalid branch data: {branch}", "WARNING")
            
            total_branches += branch_count
            has_next_page = page_info.get("hasNextPage", False)
            variables["cursor"] = page_info.get("endCursor") if has_next_page else None
        
        except Exception as e:
            log_exception(e, "Error fetching branches")
            break
    
    debug_log(f"Completed fetching {total_branches} branches, returning {len(branches)} valid branches")
    return branches

async def _fetch_recent_commits(owner: str, repo: str, graphql_url: str, 
                              graphql_headers: Dict[str, str], max_commits: int) -> Dict[str, Dict]:
    """Fetch recent commits using GraphQL API"""
    commits = {}
    debug_log(f"Fetching up to {max_commits} recent commits for {owner}/{repo}")
    query = """
    query ($owner: String!, $repo: String!, $cursor: String) {
      repository(owner: $owner, name: $repo) {
        defaultBranchRef {
          target {
            ... on Commit {
              history(first: 100, after: $cursor) {
                pageInfo {
                  hasNextPage
                  endCursor
                }
                nodes {
                  oid
                  messageHeadline
                  committedDate
                  author {
                    name
                  }
                  parents(first: 10) {
                    nodes {
                      oid
                    }
                  }
                }
              }
            }
          }
        }
      }
    }
    """
    
    variables = {
        "owner": owner,
        "repo": repo,
        "cursor": None
    }
    
    has_next_page = True
    commit_count = 0
    
    while has_next_page and commit_count < max_commits:
        try:
            debug_log(f"Making GraphQL request for recent commits, cursor: {variables['cursor']}")
            response = requests.post(
                graphql_url,
                headers=graphql_headers,
                json={"query": query, "variables": variables}
            )
            response.raise_for_status()
            result = response.json()
            
            if "errors" in result:
                debug_log(f"GraphQL Error: {result['errors']}", "ERROR")
                break
            
            repository = result.get("data", {}).get("repository", {})
            if not repository:
                debug_log("No repository data returned from GraphQL", "ERROR")
                break
                
            default_branch_ref = repository.get("defaultBranchRef", {})
            if not default_branch_ref:
                debug_log("No default branch reference found", "ERROR")
                break
                
            target = default_branch_ref.get("target", {})
            if not target:
                debug_log("No target found for default branch", "ERROR")
                break
                
            history = target.get("history", {})
            if not history:
                debug_log("No history found for default branch", "ERROR")
                break
                
            page_info = history.get("pageInfo", {})
            commit_nodes = history.get("nodes", [])
            
            page_commit_count = len(commit_nodes)
            debug_log(f"Retrieved {page_commit_count} commits in this page")
            
            for commit in commit_nodes:
                sha = commit.get("oid")
                if not sha:
                    debug_log(f"Skipping commit with no SHA", "WARNING")
                    continue
                    
                if sha not in commits and commit_count < max_commits:
                    parent_shas = [p.get("oid") for p in commit.get("parents", {}).get("nodes", []) if p.get("oid")]
                    commits[sha] = {
                        'message': commit.get("messageHeadline", ""),
                        'parents': parent_shas,
                        'date': commit.get("committedDate", ""),
                        'author': commit.get("author", {}).get("name", "Unknown"),
                        'children': []
                    }
                    commit_count += 1
            
            has_next_page = page_info.get("hasNextPage", False) and commit_count < max_commits
            variables["cursor"] = page_info.get("endCursor") if has_next_page else None
        
        except Exception as e:
            log_exception(e, "Error fetching recent commits")
            break
    
    debug_log(f"Completed fetching {commit_count} recent commits")
    return commits

async def _fetch_commit_history(owner: str, repo: str, starting_commit: str, graphql_url: str,
                              graphql_headers: Dict[str, str], traverse_direction: str, 
                              max_depth: int) -> Dict[str, Dict]:
    """Fetch commit history from a starting point"""
    commits = {}
    debug_log(f"Fetching commit history for {owner}/{repo} starting from {starting_commit}, direction: {traverse_direction}, max depth: {max_depth}")
    
    # First, get the starting commit
    query = """
    query ($owner: String!, $repo: String!, $sha: GitObjectID!) {
      repository(owner: $owner, name: $repo) {
        object(oid: $sha) {
          ... on Commit {
            oid
            messageHeadline
            committedDate
            author {
              name
            }
            parents(first: 10) {
              nodes {
                oid
              }
            }
          }
        }
      }
    }
    """
    
    variables = {
        "owner": owner,
        "repo": repo,
        "sha": starting_commit
    }
    
    try:
        debug_log(f"Making GraphQL request to fetch starting commit {starting_commit}")
        response = requests.post(
            graphql_url,
            headers=graphql_headers,
            json={"query": query, "variables": variables}
        )
        response.raise_for_status()
        result = response.json()
        
        if "errors" in result:
            debug_log(f"GraphQL Error: {result['errors']}", "ERROR")
            return commits
            
        repository_object = result.get("data", {}).get("repository", {}).get("object")
        if not repository_object:
            debug_log(f"Commit {starting_commit} not found in repository", "ERROR")
            return commits
        
        # Add the starting commit
        commit = repository_object
        sha = commit.get("oid")
        if not sha:
            debug_log("Starting commit has no SHA", "ERROR")
            return commits
            
        parent_shas = [p.get("oid") for p in commit.get("parents", {}).get("nodes", []) if p.get("oid")]
        commits[sha] = {
            'message': commit.get("messageHeadline", ""),
            'parents': parent_shas,
            'date': commit.get("committedDate", ""),
            'author': commit.get("author", {}).get("name", "Unknown"),
            'children': []
        }
        debug_log(f"Added starting commit {sha} with {len(parent_shas)} parents")
        
        # For backward traversal (ancestors)
        if traverse_direction.lower() == "backward":
            debug_log("Performing backward traversal to find ancestors")
            commits_to_process = parent_shas.copy()
            processed_commits = {sha}
            depth = 0
            
            while commits_to_process and depth < max_depth and len(commits) < max_depth:
                current_sha = commits_to_process.pop(0)
                debug_log(f"Processing ancestor commit: {current_sha}")
                
                if current_sha in processed_commits:
                    debug_log(f"Skipping already processed commit: {current_sha}")
                    continue
                
                # Fetch this commit
                variables["sha"] = current_sha
                response = requests.post(
                    graphql_url,
                    headers=graphql_headers,
                    json={"query": query, "variables": variables}
                )
                
                if response.status_code == 200:
                    result = response.json()
                    repository_object = result.get("data", {}).get("repository", {}).get("object")
                    
                    if repository_object:
                        commit = repository_object
                        parent_shas = [p.get("oid") for p in commit.get("parents", {}).get("nodes", []) if p.get("oid")]
                        
                        commits[current_sha] = {
                            'message': commit.get("messageHeadline", ""),
                            'parents': parent_shas,
                            'date': commit.get("committedDate", ""),
                            'author': commit.get("author", {}).get("name", "Unknown"),
                            'children': []
                        }
                        
                        debug_log(f"Added ancestor commit {current_sha} with {len(parent_shas)} parents")
                        processed_commits.add(current_sha)
                        
                        # Add parents to processing queue
                        for parent in parent_shas:
                            if parent not in processed_commits and parent not in commits_to_process:
                                debug_log(f"Adding parent {parent} to processing queue")
                                commits_to_process.append(parent)
                    else:
                        debug_log(f"Commit {current_sha} not found in repository", "WARNING")
                else:
                    debug_log(f"Failed to fetch commit {current_sha}: {response.status_code}", "ERROR")
                
                depth += 1
            
            debug_log(f"Completed backward traversal with {len(commits)} commits")
            
        # For forward traversal (descendants), we need to get more commits first
        elif traverse_direction.lower() == "forward":
            debug_log("Performing forward traversal to find descendants")
            # For forward traversal, we need to fetch more commits to find potential descendants
            debug_log("Fetching recent commits to identify potential descendants")
            recent_commits = await _fetch_recent_commits(owner, repo, graphql_url, graphql_headers, 500)
            
            # Add recent commits to our commits dictionary
            for sha, commit_data in recent_commits.items():
                if sha not in commits:
                    commits[sha] = commit_data
            
            debug_log(f"Building parent-child relationships among {len(commits)} commits")
            # Build parent-child relationships
            for sha, commit_data in commits.items():
                for parent_sha in commit_data.get('parents', []):
                    if parent_sha in commits and sha not in commits[parent_sha].get('children', []):
                        if 'children' not in commits[parent_sha]:
                            commits[parent_sha]['children'] = []
                        commits[parent_sha]['children'].append(sha)
            
            # Now traverse forward from the starting commit
            commits_to_keep = {starting_commit}
            if starting_commit in commits and 'children' in commits[starting_commit]:
                commits_to_process = list(commits[starting_commit]['children'])
                debug_log(f"Starting forward traversal with {len(commits_to_process)} direct descendants")
            else:
                debug_log("No direct descendants found for starting commit", "WARNING")
                commits_to_process = []
                
            processed = {starting_commit}
            depth = 0
            
            while commits_to_process and depth < max_depth:
                current_sha = commits_to_process.pop(0)
                debug_log(f"Processing descendant commit: {current_sha}")
                
                if current_sha in processed:
                    debug_log(f"Skipping already processed commit: {current_sha}")
                    continue
                
                processed.add(current_sha)
                commits_to_keep.add(current_sha)
                
                # Add children to processing queue
                if current_sha in commits:
                    children = commits[current_sha].get('children', [])
                    debug_log(f"Found {len(children)} children for commit {current_sha}")
                    for child_sha in children:
                        if child_sha not in processed and child_sha not in commits_to_process:
                            debug_log(f"Adding child {child_sha} to processing queue")
                            commits_to_process.append(child_sha)
                
                depth += 1
            
            # Filter commits to keep only descendants and starting commit
            debug_log(f"Filtering to keep only starting commit and its descendants ({len(commits_to_keep)} commits)")
            commits = {sha: commit_data for sha, commit_data in commits.items() if sha in commits_to_keep}
            debug_log(f"Completed forward traversal with {len(commits)} commits")
    
    except Exception as e:
        log_exception(e, f"Error fetching commit history for {starting_commit}")
    
    return commits

async def _find_branches_for_commit(owner: str, repo: str, commit_sha: str, branches: Dict[str, str],
                                   graphql_url: str, graphql_headers: Dict[str, str]) -> List[str]:
    """Find branches that contain a specific commit"""
    debug_log(f"Finding branches containing commit {commit_sha}")
    # Direct branch reference (commit is branch head)
    direct_branches = [name for name, sha in branches.items() if sha == commit_sha]
    if direct_branches:
        debug_log(f"Found {len(direct_branches)} branches directly pointing to commit {commit_sha}")
        return direct_branches
    
    # Check a reasonable number of branches
    branches_to_check = list(branches.keys())
    if len(branches_to_check) > 10:
        debug_log(f"Too many branches ({len(branches_to_check)}), filtering to prioritize common ones")
        # Prioritize common branch names
        priority_branches = ['main', 'master', 'develop', 'dev', 'release']
        filtered_branches = [b for b in branches_to_check if any(p in b.lower() for p in priority_branches)]
        debug_log(f"Found {len(filtered_branches)} priority branches")
        # Add some other branches (limit to 15 total)
        remaining_slots = 15 - len(filtered_branches)
        if remaining_slots > 0:
            filtered_branches.extend([b for b in branches_to_check if b not in filtered_branches][:remaining_slots])
        branches_to_check = filtered_branches
    
    debug_log(f"Checking {len(branches_to_check)} branches for commit {commit_sha}")
    containing_branches = []
    query = """
    query ($owner: String!, $repo: String!, $commitOid: GitObjectID!, $branchName: String!) {
      repository(owner: $owner, name: $repo) {
        ref(qualifiedName: $branchName) {
          compare(headRef: $branchName, baseOid: $commitOid) {
            status
          }
        }
      }
    }
    """
    
    for branch_name in branches_to_check:
        variables = {
            "owner": owner,
            "repo": repo,
            "commitOid": commit_sha,
            "branchName": f"refs/heads/{branch_name}"
        }
        
        try:
            debug_log(f"Checking if branch '{branch_name}' contains commit {commit_sha}")
            response = requests.post(
                graphql_url,
                headers=graphql_headers,
                json={"query": query, "variables": variables}
            )
            
            if response.status_code == 200:
                data = response.json()
                ref_data = data.get("data", {}).get("repository", {}).get("ref", {})
                if ref_data and ref_data.get("compare", {}).get("status") in ["AHEAD", "IDENTICAL"]:
                    debug_log(f"Branch '{branch_name}' contains commit {commit_sha}")
                    containing_branches.append(branch_name)
        except Exception as e:
            log_exception(e, f"Error checking if branch {branch_name} contains commit {commit_sha}")
    
    debug_log(f"Found {len(containing_branches)} branches containing commit {commit_sha}")
    return containing_branches

if __name__ == "__main__":
    debug_log("Starting GitHub MCP server")
    mcp.run()

"""
GitHub MCP Server

This server provides tools to analyze GitHub repositories, with a focus on
pull requests and commits. It helps answer questions like:

- What commits are in a specific pull request?
- Who authored the commits and when?
- What are the details of a specific commit?
- What pull requests exist in a repository?
- What are the PR statistics for a repository?
- What branches contain a specific commit?
- How has a commit been promoted through branches?

Usage:
1. Set GITHUB_PERSONAL_ACCESS_TOKEN or GITHUB_TOKEN environment variable
2. Run this script to start the server
3. Use with any MCP client like Claude or other AI tools

API Tools:
- find_pr_commits: Get all commits for a specific PR with author and timestamp info
- list_repo_pull_requests: Get PRs in a repository with filtering options
- get_commit_details: Get detailed information about a specific commit
- get_pr_stats: Get statistics about PRs in a repository over a time period
- get_pr_approvals: Get approval events and review information for a pull request
- find_commit_branches: Find branches that contain a specific commit
- get_commit_graph: Generate a visualization-ready JSON of the commit graph
- trace_commit_path: Trace a commit's path through branches to understand its promotion history
"""
