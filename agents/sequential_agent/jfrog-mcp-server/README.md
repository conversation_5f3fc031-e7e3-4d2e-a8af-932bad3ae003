# JFrog Artifactory MCP Server

This is a Model Context Protocol (MCP) server that provides tools for interacting with JFrog Artifactory to retrieve artifact metadata and search for artifacts.

## Features

The JFrog MCP server provides the following tools:

### 1. `list_recent_artifacts`
Lists recently created artifacts in JFrog Artifactory.

**Parameters:**
- `from_index` (int, optional): Starting index for results (default: 0)
- `to_index` (int, optional): Ending index for results (default: 10)

**Returns:** List of recently created artifacts with their URIs.

### 2. `get_artifact_metadata`
Retrieves full metadata for a specific artifact.

**Parameters:**
- `repo` (str): Repository name (e.g., 'generic-local')
- `path` (str): Artifact path (e.g., 'my-app/1.0.0/my-app-1.0.0.jar')

**Returns:** Complete artifact metadata including checksums, properties, and timestamps.

### 3. `search_artifacts_by_property`
Searches for artifacts by property value.

**Parameters:**
- `property_name` (str): Name of the property to search for (e.g., 'commit.sha')
- `property_value` (str): Value of the property to search for (e.g., 'abc123')

**Returns:** List of artifacts matching the specified property.

### 4. `check_jfrog_connection`
Verifies connection to JFrog Artifactory and returns server information.

**Returns:** Connection status, server version, and basic server information.

### 5. `get_artifact_by_commit_sha`
Finds all artifacts associated with a specific git commit SHA.

**Parameters:**
- `commit_sha` (str): Git commit SHA hash to search for

**Returns:** Enriched artifact information including metadata for artifacts linked to the commit.

## Setup

### Environment Variables

Create a `.env` file with the following variables:

```env
JFROG_URL=https://your-instance.jfrog.io
JFROG_USERNAME=your-username
JFROG_PASSWORD=your-password-or-api-key
```

### Dependencies

The server requires the following Python packages:
- `mcp`
- `requests`
- `python-dotenv`

## Usage

### As a Standalone Server

```bash
python main.py
```

### As part of Sequential Agent

The JFrog MCP server is integrated into the sequential agent pipeline to:

1. **Artifact Discovery**: Find artifacts associated with commits from GitHub PRs
2. **Metadata Collection**: Gather detailed artifact information including checksums and properties
3. **Build Correlation**: Link artifacts to specific builds and deployments
4. **Environment Tracking**: Track artifact deployment across different environments

## API Examples

The server implements the JFrog Artifactory REST API endpoints:

### List Recent Artifacts
```
GET https://your-instance.jfrog.io/artifactory/api/search/creation?from=0&to=10
```

### Get Artifact Metadata
```
GET https://your-instance.jfrog.io/artifactory/api/storage/repo-name/path/to/artifact
```

### Search by Property
```
GET https://your-instance.jfrog.io/artifactory/api/search/prop?commit.sha=abc123
```

## Integration with DORA Metrics

This JFrog MCP server enhances the DORA metrics pipeline by providing:

- **Deployment Frequency**: Track when artifacts are deployed to different environments
- **Lead Time**: Measure time from commit to artifact deployment
- **Change Failure Rate**: Identify failed deployments through artifact status
- **Recovery Time**: Track time to recover from failed artifact deployments

The collected artifact data is merged with GitHub PR data and Jenkins build data to provide comprehensive DORA metrics analysis. 