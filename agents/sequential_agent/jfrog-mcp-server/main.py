import asyncio
import json
import os
import sys
import traceback
from typing import Dict, List, Optional, Any, AsyncIterator
from datetime import datetime, timezone
from contextlib import asynccontextmanager
import requests
from mcp.server.fastmcp import FastMCP, Context

def debug_log(message: str, level: str = "INFO"):
    """Write log messages to stderr with severity level
    
    Args:
        message: The message to log
        level: Severity level (INFO, WARNING, ERROR)
    """
    level = level.upper()
    print(f"JFROG-MCP [{level}]: {message}", file=sys.stderr)

def log_exception(e: Exception, context: str = ""):
    """Log exception with traceback
    
    Args:
        e: The exception
        context: Additional context about where the exception occurred
    """
    error_msg = f"{context}: {str(e)}" if context else str(e)
    debug_log(error_msg, "ERROR")
    debug_log(traceback.format_exc(), "ERROR")

class JFrogAPI:
    """JFrog Artifactory API client with proper error handling"""
    
    def __init__(self, base_url: str, username: str = "", password: str = "", access_token: str = ""):
        # Ensure the base URL is properly formatted for Artifactory API
        base_url = base_url.rstrip('/')
        if not base_url.endswith('/artifactory'):
            if '/artifactory' not in base_url:
                base_url = f"{base_url}/artifactory"
        self.base_url = base_url
        self.session = requests.Session()
        self.auth_method = "none"
        
        # Set up authentication
        if access_token:
            self.session.headers.update({
                'Authorization': f'Bearer {access_token}',
                'Accept': 'application/json',
                'User-Agent': 'JFrog-MCP-Server/1.0'
            })
            self.auth_method = "access_token"
            debug_log("Using access token authentication")
        elif username and password:
            # Check if password looks like an API key
            if len(password) > 20 and any(c.isdigit() for c in password) and any(c.isalpha() for c in password):
                self.auth_method = "api_key"
                debug_log("Using API key authentication")
            else:
                self.auth_method = "basic"
                debug_log("Using basic authentication")
            
            self.session.auth = (username, password)
            self.session.headers.update({
                'Accept': 'application/json',
                'User-Agent': 'JFrog-MCP-Server/1.0'
            })
        else:
            raise ValueError("No valid authentication credentials provided")
    
    def _make_request(self, method: str, endpoint: str, **kwargs) -> Dict[str, Any]:
        """Make a request to the JFrog API with proper error handling
        
        Args:
            method: HTTP method (GET, POST, etc.)
            endpoint: API endpoint (without base URL)
            **kwargs: Additional arguments to pass to requests
            
        Returns:
            Dict containing the response data
            
        Raises:
            requests.exceptions.RequestException: If the request fails
        """
        url = f"{self.base_url}{endpoint}"
        try:
            debug_log(f"Making {method} request to: {url}")
            response = self.session.request(method, url, timeout=30, **kwargs)
            debug_log(f"Response status: {response.status_code}")
            
            if response.status_code == 404:
                raise ValueError(f"Resource not found: {endpoint}")
            elif response.status_code == 403:
                raise ValueError("Insufficient permissions or authentication failed")
            elif response.status_code == 406:
                raise ValueError("Request not acceptable - check Accept headers")
            elif response.status_code >= 400:
                raise ValueError(f"JFrog API error {response.status_code}: {response.text}")
            
            # Handle different content types
            content_type = response.headers.get('content-type', '').lower()
            if 'application/json' in content_type:
                return response.json()
            else:
                return {"text": response.text, "status_code": response.status_code}
                
        except requests.exceptions.RequestException as e:
            debug_log(f"Request failed: {str(e)}", "ERROR")
            raise ValueError(f"JFrog API request failed: {str(e)}")
    
    def ping(self) -> Dict[str, Any]:
        """Ping the JFrog server"""
        # Use text/plain accept header for ping endpoint
        headers = {'Accept': 'text/plain, */*'}
        return self._make_request("GET", "/api/system/ping", headers=headers)
    
    def get_version(self) -> Dict[str, Any]:
        """Get JFrog server version information"""
        return self._make_request("GET", "/api/system/version")
    
    def list_repositories(self) -> List[Dict[str, Any]]:
        """List all repositories"""
        return self._make_request("GET", "/api/repositories")
    
    def search_creation(self, from_index: int = 0, to_index: int = 10) -> Dict[str, Any]:
        """Search artifacts by creation date using AQL"""
        # Use AQL to find recent artifacts, which is more reliable
        limit = to_index - from_index + 1
        aql_query = f'''
        items.find({{
            "type": "file"
        }}).sort({{
            "$desc": ["created"]
        }}).offset({from_index}).limit({limit})
        '''
        
        headers = {'Content-Type': 'text/plain'}
        try:
            response = self.session.post(
                f"{self.base_url}/api/search/aql",
                data=aql_query.strip(),
                headers=headers,
                timeout=30
            )
            
            if response.status_code == 200:
                data = response.json()
                results = data.get('results', [])
                
                # Convert AQL results to match the expected format
                formatted_results = []
                for item in results:
                    repo = item.get('repo', '')
                    path = item.get('path', '')
                    name = item.get('name', '')
                    
                    # Construct URI
                    if path and path != '.':
                        full_path = f"{path}/{name}"
                    else:
                        full_path = name
                    
                    uri = f"{self.base_url.replace('/artifactory', '')}/artifactory/{repo}/{full_path}"
                    
                    formatted_results.append({
                        "uri": uri,
                        "repo": repo,
                        "path": full_path,
                        "name": name,
                        "created": item.get('created', ''),
                        "size": item.get('size', 0),
                        "sha1": item.get('actual_sha1', ''),
                        "md5": item.get('actual_md5', '')
                    })
                
                return {"results": formatted_results}
            else:
                raise ValueError(f"AQL search failed: {response.status_code} - {response.text}")
                
        except Exception as e:
            # Fallback to original search/creation endpoint
            debug_log(f"AQL search failed, trying original endpoint: {str(e)}", "WARNING")
            params = {'from': from_index, 'to': to_index}
            return self._make_request("GET", "/api/search/creation", params=params)
    
    def get_artifact_metadata(self, repo: str, path: str) -> Dict[str, Any]:
        """Get artifact metadata"""
        return self._make_request("GET", f"/api/storage/{repo}/{path}")
    
    def search_by_property(self, property_name: str, property_value: str) -> Dict[str, Any]:
        """Search artifacts by property using AQL"""
        # Use AQL to search by properties, which is more reliable
        aql_query = f'''
        items.find({{
            "type": "file",
            "@{property_name}": "{property_value}"
        }}).limit(100)
        '''
        
        headers = {'Content-Type': 'text/plain'}
        try:
            response = self.session.post(
                f"{self.base_url}/api/search/aql",
                data=aql_query.strip(),
                headers=headers,
                timeout=30
            )
            
            if response.status_code == 200:
                data = response.json()
                results = data.get('results', [])
                
                # Convert AQL results to match the expected format
                formatted_results = []
                for item in results:
                    repo = item.get('repo', '')
                    path = item.get('path', '')
                    name = item.get('name', '')
                    
                    # Construct URI
                    if path and path != '.':
                        full_path = f"{path}/{name}"
                    else:
                        full_path = name
                    
                    uri = f"{self.base_url.replace('/artifactory', '')}/artifactory/{repo}/{full_path}"
                    
                    formatted_results.append({
                        "uri": uri,
                        "repo": repo,
                        "path": full_path,
                        "name": name,
                        "created": item.get('created', ''),
                        "size": item.get('size', 0),
                        "sha1": item.get('actual_sha1', ''),
                        "md5": item.get('actual_md5', '')
                    })
                
                return {"results": formatted_results}
            else:
                raise ValueError(f"AQL property search failed: {response.status_code} - {response.text}")
                
        except Exception as e:
            # Fallback to original search/prop endpoint
            debug_log(f"AQL property search failed, trying original endpoint: {str(e)}", "WARNING")
            params = {property_name: property_value}
            return self._make_request("GET", "/api/search/prop", params=params)

debug_log("Starting JFrog MCP server...")

@asynccontextmanager
async def app_lifespan(server: FastMCP) -> AsyncIterator[JFrogAPI]:
    """Manage JFrog API client lifecycle"""
    debug_log("Starting JFrog API client lifespan")
    jfrog_client = None
    
    try:
        # Load environment variables
        try:
            from dotenv import load_dotenv
            debug_log("Loading environment variables from .env file")
            load_dotenv(verbose=False, override=False)
            debug_log("Successfully loaded .env file")
        except ImportError:
            debug_log("python-dotenv not installed, using system environment variables")
        except Exception as e:
            debug_log(f"Warning: Could not load .env file: {str(e)}", "WARNING")
        
        # Get configuration from environment
        base_url = os.getenv("JFROG_URL")
        if not base_url:
            raise ValueError("JFROG_URL environment variable is required")
        
        access_token = os.getenv("JFROG_ACCESS_TOKEN", "")
        username = os.getenv("JFROG_USERNAME", "")
        password = os.getenv("JFROG_PASSWORD", "")
        
        if not access_token and not (username and password):
            raise ValueError("No valid authentication credentials found. Please set either JFROG_ACCESS_TOKEN or both JFROG_USERNAME and JFROG_PASSWORD")
        
        debug_log(f"Connecting to JFrog at {base_url}")
        debug_log(f"JFROG_URL: {base_url}")
        debug_log(f"JFROG_USERNAME: {'Set' if username else 'Not set'}")
        debug_log(f"JFROG_PASSWORD: {'Set' if password else 'Not set'}")
        debug_log(f"JFROG_ACCESS_TOKEN: {'Set' if access_token else 'Not set'}")
        
        # Initialize JFrog client
        jfrog_client = JFrogAPI(base_url, username, password, access_token)
        
        # Test connection
        debug_log("Testing connection to JFrog Artifactory")
        try:
            ping_result = jfrog_client.ping()
            debug_log("Ping successful")
        except Exception as e:
            debug_log(f"Ping failed, trying version endpoint: {str(e)}", "WARNING")
            try:
                version_result = jfrog_client.get_version()
                debug_log("Version endpoint successful")
            except Exception as e2:
                debug_log(f"Version endpoint also failed: {str(e2)}", "ERROR")
                raise ConnectionError(f"Failed to connect to JFrog Artifactory: {str(e2)}")
        
        debug_log("JFrog API client initialized successfully")
        yield jfrog_client
        
    except Exception as e:
        log_exception(e, "Error in JFrog API client lifespan")
        raise
    finally:
        if jfrog_client and hasattr(jfrog_client, 'session'):
            try:
                debug_log("Closing JFrog API client")
                jfrog_client.session.close()
            except Exception as e:
                debug_log(f"Error closing session: {str(e)}", "WARNING")
        debug_log("Exiting JFrog API client lifespan")

# Initialize the MCP server
mcp = FastMCP("JFrog Artifactory", lifespan=app_lifespan, request_timeout=300)

@mcp.tool()
async def list_recent_artifacts(ctx: Context, from_index: int = 0, to_index: int = 10) -> str:
    """List recently created artifacts in JFrog Artifactory
    
    Uses the /api/search/creation endpoint to get the latest created artifacts.
    
    Args:
        from_index: Starting index for results (default: 0)
        to_index: Ending index for results (default: 10)
        
    Returns:
        JSON string containing list of recently created artifacts with their URIs
    """
    debug_log(f"Listing recent artifacts from {from_index} to {to_index}")
    
    try:
        jfrog_api = ctx.request_context.lifespan_context
        result = jfrog_api.search_creation(from_index, to_index)
        
        artifacts = result.get('results', [])
        debug_log(f"Successfully retrieved {len(artifacts)} artifacts")
        
        response = {
            "status": "success",
            "artifacts": artifacts,
            "total_count": len(artifacts),
            "from_index": from_index,
            "to_index": to_index
        }
        
        return json.dumps(response, indent=2)
        
    except Exception as e:
        log_exception(e, "Error listing recent artifacts")
        error_response = {
            "status": "error",
            "error": str(e),
            "artifacts": []
        }
        return json.dumps(error_response, indent=2)

@mcp.tool()
async def get_artifact_metadata(ctx: Context, repo: str, path: str) -> str:
    """Get full metadata for a specific artifact
    
    Uses the /api/storage/<repo>/<path> endpoint to get complete artifact metadata.
    
    Args:
        repo: Repository name (e.g., 'generic-local')
        path: Artifact path (e.g., 'my-app/1.0.0/my-app-1.0.0.jar')
        
    Returns:
        JSON string containing complete artifact metadata including checksums and properties
    """
    debug_log(f"Getting metadata for artifact: {repo}/{path}")
    
    try:
        jfrog_api = ctx.request_context.lifespan_context
        metadata = jfrog_api.get_artifact_metadata(repo, path)
        
        debug_log(f"Successfully retrieved metadata for {repo}/{path}")
        
        response = {
            "status": "success",
            "metadata": metadata
        }
        
        return json.dumps(response, indent=2)
        
    except Exception as e:
        log_exception(e, f"Error getting metadata for {repo}/{path}")
        error_response = {
            "status": "error",
            "error": str(e),
            "metadata": {}
        }
        return json.dumps(error_response, indent=2)

@mcp.tool()
async def search_artifacts_by_property(ctx: Context, property_name: str, property_value: str) -> str:
    """Search artifacts by property value
    
    Uses the /api/search/prop endpoint to find artifacts with specific properties.
    
    Args:
        property_name: Name of the property to search for (e.g., 'commit.sha')
        property_value: Value of the property to search for (e.g., 'abc123')
        
    Returns:
        JSON string containing list of artifacts matching the property
    """
    debug_log(f"Searching artifacts by property: {property_name}={property_value}")
    
    try:
        jfrog_api = ctx.request_context.lifespan_context
        result = jfrog_api.search_by_property(property_name, property_value)
        
        artifacts = result.get('results', [])
        debug_log(f"Successfully found {len(artifacts)} artifacts with property {property_name}={property_value}")
        
        response = {
            "status": "success",
            "artifacts": artifacts,
            "property_name": property_name,
            "property_value": property_value,
            "total_count": len(artifacts)
        }
        
        return json.dumps(response, indent=2)
        
    except Exception as e:
        log_exception(e, f"Error searching by property {property_name}={property_value}")
        error_response = {
            "status": "error",
            "error": str(e),
            "artifacts": []
        }
        return json.dumps(error_response, indent=2)

@mcp.tool()
async def get_artifact_metadata_from_uri(ctx: Context, artifact_uri: str) -> str:
    """Get full metadata for an artifact using its URI
    
    Convenience function that extracts repo and path from a URI and calls get_artifact_metadata.
    
    Args:
        artifact_uri: Full artifact URI (e.g., 'https://instance.jfrog.io/artifactory/repo/path/file.jar')
        
    Returns:
        JSON string containing complete artifact metadata
    """
    debug_log(f"Getting metadata for artifact URI: {artifact_uri}")
    
    try:
        # Parse URI to extract repo and path
        if "/artifactory/" not in artifact_uri:
            raise ValueError("Invalid URI format: must contain '/artifactory/'")
        
        parts = artifact_uri.split("/artifactory/", 1)
        if len(parts) != 2:
            raise ValueError("Invalid URI format: could not parse artifactory path")
        
        repo_path = parts[1]
        repo_parts = repo_path.split("/", 1)
        
        if len(repo_parts) != 2:
            raise ValueError("Invalid URI format: could not extract repo and path")
        
        repo = repo_parts[0]
        path = repo_parts[1]
        
        debug_log(f"Extracted repo: {repo}, path: {path}")
        
        # Call the existing get_artifact_metadata function
        return await get_artifact_metadata(ctx, repo, path)
        
    except Exception as e:
        log_exception(e, f"Error parsing URI: {artifact_uri}")
        error_response = {
            "status": "error",
            "error": str(e),
            "metadata": {}
        }
        return json.dumps(error_response, indent=2)

@mcp.tool()
async def check_jfrog_connection(ctx: Context) -> str:
    """Check connection to JFrog Artifactory server and get basic information
    
    Returns:
        JSON string containing connection status and server information
    """
    debug_log("Checking JFrog connection")
    
    try:
        jfrog_api = ctx.request_context.lifespan_context
        
        # Test ping
        try:
            ping_result = jfrog_api.ping()
            ping_success = True
            ping_response = ping_result.get("text", "OK")
        except Exception as e:
            ping_success = False
            ping_response = str(e)
        
        # Test version
        try:
            version_result = jfrog_api.get_version()
            version_success = True
            version_data = version_result
        except Exception as e:
            version_success = False
            version_data = {"error": str(e)}
        
        # Test repositories
        try:
            repos_result = jfrog_api.list_repositories()
            repos_success = True
            repos_count = len(repos_result) if isinstance(repos_result, list) else 0
        except Exception as e:
            repos_success = False
            repos_count = f"Error: {str(e)}"
        
        response = {
            "status": "connected" if ping_success or version_success else "error",
            "auth_method": jfrog_api.auth_method,
            "ping": {
                "success": ping_success,
                "response": ping_response
            },
            "version": {
                "success": version_success,
                "version": version_data.get("version", "Unknown") if version_success else "Unknown",
                "revision": version_data.get("revision", "Unknown") if version_success else "Unknown",
                "addons": version_data.get("addons", []) if version_success else []
            },
            "repositories": {
                "success": repos_success,
                "count": repos_count
            },
            "url": jfrog_api.base_url
        }
        
        return json.dumps(response, indent=2)
        
    except Exception as e:
        log_exception(e, "Error checking JFrog connection")
        error_response = {
            "status": "error",
            "error": str(e)
        }
        return json.dumps(error_response, indent=2)

@mcp.tool()
async def diagnose_jfrog_connectivity(ctx: Context) -> str:
    """Comprehensive diagnostic tool for JFrog connectivity issues
    
    Returns:
        JSON string containing detailed diagnostic information
    """
    debug_log("Running JFrog connectivity diagnostics")
    
    try:
        jfrog_api = ctx.request_context.lifespan_context
        
        diagnostics = {
            "timestamp": datetime.now(timezone.utc).isoformat(),
            "auth_method": jfrog_api.auth_method,
            "base_url": jfrog_api.base_url,
            "tests": {}
        }
        
        # Test 1: Ping
        try:
            ping_result = jfrog_api.ping()
            diagnostics["tests"]["ping"] = {
                "success": True,
                "response": ping_result.get("text", "OK"),
                "error": None
            }
        except Exception as e:
            diagnostics["tests"]["ping"] = {
                "success": False,
                "error": str(e)
            }
        
        # Test 2: Version
        try:
            version_result = jfrog_api.get_version()
            diagnostics["tests"]["version"] = {
                "success": True,
                "data": version_result,
                "error": None
            }
        except Exception as e:
            diagnostics["tests"]["version"] = {
                "success": False,
                "error": str(e)
            }
        
        # Test 3: Repositories
        try:
            repos_result = jfrog_api.list_repositories()
            diagnostics["tests"]["repositories"] = {
                "success": True,
                "count": len(repos_result) if isinstance(repos_result, list) else 0,
                "sample_repos": repos_result[:3] if isinstance(repos_result, list) else [],
                "error": None
            }
        except Exception as e:
            diagnostics["tests"]["repositories"] = {
                "success": False,
                "error": str(e)
            }
        
        # Test 4: Search
        try:
            search_result = jfrog_api.search_creation(0, 1)
            diagnostics["tests"]["search"] = {
                "success": True,
                "error": None
            }
        except Exception as e:
            diagnostics["tests"]["search"] = {
                "success": False,
                "error": str(e)
            }
        
        # Summary
        successful_tests = sum(1 for test in diagnostics["tests"].values() if test.get("success", False))
        total_tests = len(diagnostics["tests"])
        
        diagnostics["summary"] = {
            "successful_tests": successful_tests,
            "total_tests": total_tests,
            "success_rate": f"{(successful_tests/total_tests)*100:.1f}%" if total_tests > 0 else "0%",
            "overall_status": "healthy" if successful_tests == total_tests else "issues_detected"
        }
        
        # Recommendations
        recommendations = []
        if not diagnostics["tests"].get("ping", {}).get("success", False):
            recommendations.append("Check network connectivity and firewall settings")
        if not diagnostics["tests"].get("version", {}).get("success", False):
            recommendations.append("Check authentication credentials")
        if not diagnostics["tests"].get("repositories", {}).get("success", False):
            recommendations.append("Check user permissions for repository access")
        if not diagnostics["tests"].get("search", {}).get("success", False):
            recommendations.append("Check user permissions for search operations")
        
        diagnostics["recommendations"] = recommendations
        
        return json.dumps(diagnostics, indent=2)
        
    except Exception as e:
        log_exception(e, "Error running diagnostics")
        error_response = {
            "status": "error",
            "error": str(e)
        }
        return json.dumps(error_response, indent=2)

@mcp.tool() 
async def get_artifact_by_commit_sha(ctx: Context, commit_sha: str) -> str:
    """Get artifacts associated with a specific git commit SHA
    
    Args:
        commit_sha: Git commit SHA hash to search for
        
    Returns:
        JSON string containing artifacts associated with the commit SHA
    """
    debug_log(f"Searching for artifacts with commit SHA: {commit_sha}")
    
    try:
        # Search for artifacts with commit.sha property
        artifacts_result_str = await search_artifacts_by_property(ctx, "commit.sha", commit_sha)
        artifacts_result = json.loads(artifacts_result_str)
        
        if artifacts_result["status"] == "error":
            return artifacts_result_str
            
        artifacts = artifacts_result["artifacts"]
        enriched_artifacts = []
        
        # Get detailed metadata for each artifact found
        for artifact in artifacts:
            try:
                uri = artifact.get("uri", "")
                if "/artifactory/" in uri:
                    # Parse URI to extract repo and path
                    parts = uri.split("/artifactory/", 1)
                    if len(parts) == 2:
                        repo_path = parts[1]
                        repo_parts = repo_path.split("/", 1)
                        if len(repo_parts) == 2:
                            repo = repo_parts[0]
                            path = repo_parts[1]
                            
                            # Get detailed metadata
                            metadata_result_str = await get_artifact_metadata(ctx, repo, path)
                            metadata_result = json.loads(metadata_result_str)
                            
                            if metadata_result["status"] == "success":
                                enriched_artifact = {
                                    "uri": uri,
                                    "repo": repo,
                                    "path": path,
                                    "metadata": metadata_result["metadata"]
                                }
                                enriched_artifacts.append(enriched_artifact)
                            else:
                                enriched_artifacts.append({
                                    "uri": uri,
                                    "repo": repo,
                                    "path": path,
                                    "metadata_error": metadata_result.get("error", "Unknown error")
                                })
                        else:
                            enriched_artifacts.append({"uri": uri, "parse_error": "Could not parse path"})
                    else:
                        enriched_artifacts.append({"uri": uri, "parse_error": "Could not parse URI"})
                else:
                    enriched_artifacts.append({"uri": uri, "parse_error": "Invalid URI format"})
                    
            except Exception as e:
                debug_log(f"Error processing artifact {artifact}: {str(e)}", "WARNING")
                enriched_artifacts.append({
                    "artifact": artifact,
                    "processing_error": str(e)
                })
        
        response = {
            "status": "success",
            "commit_sha": commit_sha,
            "artifacts": enriched_artifacts,
            "total_count": len(enriched_artifacts)
        }
        
        return json.dumps(response, indent=2)
        
    except Exception as e:
        log_exception(e, f"Error getting artifacts by commit SHA: {commit_sha}")
        error_response = {
            "status": "error",
            "error": str(e),
            "artifacts": []
        }
        return json.dumps(error_response, indent=2)

if __name__ == "__main__":
    try:
        debug_log("Starting JFrog MCP server")
        mcp.run()
    except KeyboardInterrupt:
        debug_log("Server stopped by user")
    except Exception as e:
        log_exception(e, "Server failed to start")
        if "JFROG_URL" in str(e):
            print("\n" + "="*60)
            print("CONFIGURATION ERROR")
            print("="*60)
            print("The JFrog MCP server requires environment variables to be set.")
            print("\nRequired:")
            print("  JFROG_URL - Your JFrog Artifactory URL")
            print("\nAuthentication (choose one):")
            print("  JFROG_ACCESS_TOKEN - Access token (recommended)")
            print("  OR")
            print("  JFROG_USERNAME + JFROG_PASSWORD - Username and password/API key")
            print("\nExample:")
            print("  export JFROG_URL=https://your-instance.jfrog.io/artifactory")
            print("  export JFROG_USERNAME=your-username")
            print("  export JFROG_PASSWORD=your-password")
            print("="*60)
        raise

"""
JFrog MCP Server

This server provides tools to interact with JFrog Artifactory, helping AI models
query and analyze artifacts, repositories, and metadata.

Features:
- List recently created artifacts
- Get detailed artifact metadata
- Search artifacts by properties (e.g., commit SHA)
- Check connectivity and diagnose issues
- Support for multiple authentication methods

Usage:
1. Set JFROG_URL environment variable
2. Set authentication: JFROG_ACCESS_TOKEN or JFROG_USERNAME + JFROG_PASSWORD
3. Run this script to start the server
4. Use with any MCP client like Claude or other AI tools

API Tools:
- list_recent_artifacts: Get recently created artifacts
- get_artifact_metadata: Get detailed metadata for specific artifacts
- search_artifacts_by_property: Find artifacts by property values
- get_artifact_metadata_from_uri: Get metadata using artifact URI
- check_jfrog_connection: Test connectivity and get server info
- diagnose_jfrog_connectivity: Comprehensive diagnostic tool
- get_artifact_by_commit_sha: Find artifacts associated with git commits
""" 