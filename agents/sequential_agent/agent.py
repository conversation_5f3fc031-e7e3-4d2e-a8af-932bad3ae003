import asyncio
import os
import logging
import time
from contextlib import AsyncExitStack
from google.adk.agents import Agent, SequentialAgent, LlmAgent
from google.adk.tools.mcp_tool.mcp_toolset import MCPToolset, StdioServerParameters
from dotenv import load_dotenv
from pydantic import BaseModel, Field
from typing import List, Optional, Any, Dict
from datetime import datetime
from google.adk.agents.callback_context import CallbackContext
from google.adk.models import LlmResponse, LlmRequest
from google.genai.types import Content, Part
import requests
import json

# Load environment variables
load_dotenv(dotenv_path=os.path.join(os.path.dirname(__file__), '..', '..', '.env'))

# Environment Variables
GITHUB_TOKEN = os.getenv("GITHUB_PERSONAL_ACCESS_TOKEN")

# Multiple Jenkins Instances
JENKINS_DEV_URL = os.getenv("JENKINS_DEV_URL")
JENKINS_DEV_USERNAME = os.getenv("JENKINS_DEV_USERNAME")
JENKINS_DEV_PASSWORD = os.getenv("JENKINS_DEV_PASSWORD")

JENKINS_UAT_URL = os.getenv("JENKINS_UAT_URL")
JENKINS_UAT_USERNAME = os.getenv("JENKINS_UAT_USERNAME")
JENKINS_UAT_PASSWORD = os.getenv("JENKINS_UAT_PASSWORD")

JENKINS_PROD_URL = os.getenv("JENKINS_PROD_URL")
JENKINS_PROD_USERNAME = os.getenv("JENKINS_PROD_USERNAME")
JENKINS_PROD_PASSWORD = os.getenv("JENKINS_PROD_PASSWORD")

# Legacy Jenkins (for backward compatibility)
JENKINS_URL = os.getenv("JENKINS_URL")
JENKINS_USERNAME = os.getenv("JENKINS_USERNAME")
JENKINS_API_TOKEN = os.getenv("JENKINS_API_TOKEN")

# JFrog Configuration
JFROG_URL = os.getenv("JFROG_URL")
JFROG_USERNAME = os.getenv("JFROG_USERNAME")
JFROG_PASSWORD = os.getenv("JFROG_PASSWORD")
JFROG_ACCESS_TOKEN = os.getenv("JFROG_ACCESS_TOKEN")

# JFrog Artifact Paths by Environment
JFROG_DEV_PATH = os.getenv("JFROG_DEV_PATH", "truxt12345678-repo-1/nextjs-builds/")
JFROG_SIT_PATH = os.getenv("JFROG_SIT_PATH", "sitCI/nextjs-builds/")
JFROG_STAGE_PATH = os.getenv("JFROG_STAGE_PATH", "stageCI/nextjs-builds/")
JFROG_PROD_PATH = os.getenv("JFROG_PROD_PATH", "prodCI/nextjs-builds/")

# DORA Metrics Configuration
DORA_ENVIRONMENTS = os.getenv("DORA_ENVIRONMENTS", "qa-dev,qa-sit,qa-stage,qa-prod").split(",")
DORA_ENVIRONMENT_MAPPING = dict(pair.split(":") for pair in os.getenv("DORA_ENVIRONMENT_MAPPING", "qa-dev:dev,qa-sit:sit,qa-stage:stage,qa-prod:prod").split(","))

GEMINI_MODEL = "gemini-2.5-flash-preview-04-17"

# Configure structured logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler('sequential_agent.log')
    ]
)
logger = logging.getLogger('sequential_agent')

class SequentialAgentState:
    """Manages state for the sequential agent and its sub-agents"""

    def __init__(self):
        self.pipeline_status = "idle"
        self.current_agent = None
        self.agents_completed = []
        self.agents_failed = []
        self.total_execution_time = 0
        self.github_agent_state = {}
        self.jenkins_agent_state = {}
        self.jfrog_agent_state = {}
        self.mapping_agent_state = {}
        self.data_flow = {}
        self.errors_count = 0
        self.start_time = None
        self.end_time = None

    def to_dict(self) -> Dict[str, Any]:
        """Convert state to dictionary for session storage"""
        return {
            "pipeline_status": self.pipeline_status,
            "current_agent": self.current_agent,
            "agents_completed": self.agents_completed,
            "agents_failed": self.agents_failed,
            "total_execution_time": self.total_execution_time,
            "github_agent_state": self.github_agent_state,
            "jenkins_agent_state": self.jenkins_agent_state,
            "jfrog_agent_state": self.jfrog_agent_state,
            "mapping_agent_state": self.mapping_agent_state,
            "data_flow": self.data_flow,
            "errors_count": self.errors_count,
            "start_time": self.start_time.isoformat() if self.start_time else None,
            "end_time": self.end_time.isoformat() if self.end_time else None
        }

    def from_dict(self, data: Dict[str, Any]):
        """Load state from dictionary"""
        self.pipeline_status = data.get("pipeline_status", "idle")
        self.current_agent = data.get("current_agent")
        self.agents_completed = data.get("agents_completed", [])
        self.agents_failed = data.get("agents_failed", [])
        self.total_execution_time = data.get("total_execution_time", 0)
        self.github_agent_state = data.get("github_agent_state", {})
        self.jenkins_agent_state = data.get("jenkins_agent_state", {})
        self.jfrog_agent_state = data.get("jfrog_agent_state", {})
        self.mapping_agent_state = data.get("mapping_agent_state", {})
        self.data_flow = data.get("data_flow", {})
        self.errors_count = data.get("errors_count", 0)
        if data.get("start_time"):
            self.start_time = datetime.fromisoformat(data["start_time"])
        if data.get("end_time"):
            self.end_time = datetime.fromisoformat(data["end_time"])

# Global state instance
sequential_agent_state = SequentialAgentState()

def log_sequential_event(level: str, message: str, **kwargs):
    """Structured logging for sequential agent events"""
    extra_info = " | ".join([f"{k}={v}" for k, v in kwargs.items()])
    full_message = f"{message} | {extra_info}" if extra_info else message

    if level.upper() == "DEBUG":
        logger.debug(full_message)
    elif level.upper() == "INFO":
        logger.info(full_message)
    elif level.upper() == "WARNING":
        logger.warning(full_message)
    elif level.upper() == "ERROR":
        logger.error(full_message)

def before_sequential_agent_callback(callback_context: CallbackContext, user_message: Content) -> Optional[Content]:
    """Callback executed before sequential agent starts processing"""
    sequential_agent_state.start_time = datetime.now()
    sequential_agent_state.pipeline_status = "running"
    sequential_agent_state.current_agent = "starting"

    log_sequential_event("INFO", "Sequential agent pipeline started",
                        agent_name=callback_context.agent_name,
                        session_id=callback_context.session.id if callback_context.session else "unknown",
                        user_message_length=len(str(user_message)))

    # Update session state
    if callback_context.session:
        callback_context.session.state["sequential_agent:pipeline_start"] = time.time()
        callback_context.session.state["sequential_agent:state"] = sequential_agent_state.to_dict()

    return None  # Allow normal execution

def after_sequential_agent_callback(callback_context: CallbackContext, agent_response: Content) -> Optional[Content]:
    """Callback executed after sequential agent completes processing"""
    sequential_agent_state.end_time = datetime.now()
    sequential_agent_state.pipeline_status = "completed"
    sequential_agent_state.current_agent = None

    if sequential_agent_state.start_time:
        sequential_agent_state.total_execution_time = (sequential_agent_state.end_time - sequential_agent_state.start_time).total_seconds()

    log_sequential_event("INFO", "Sequential agent pipeline completed",
                        agent_name=callback_context.agent_name,
                        total_execution_time=sequential_agent_state.total_execution_time,
                        agents_completed=len(sequential_agent_state.agents_completed),
                        agents_failed=len(sequential_agent_state.agents_failed),
                        response_length=len(str(agent_response)))

    # Update session state with completion metrics
    if callback_context.session:
        callback_context.session.state["sequential_agent:pipeline_end"] = time.time()
        callback_context.session.state["sequential_agent:total_execution_time"] = sequential_agent_state.total_execution_time
        callback_context.session.state["sequential_agent:state"] = sequential_agent_state.to_dict()

    return None  # Use original response  # Ensure API key is in .env

# Nested models
class CommitPromotionPath(BaseModel):
    environment: str = Field(description="The environment name (e.g., develop, UAT, staging, main)")
    timestamp: datetime = Field(description="ISO timestamp when the commit entered this environment")
    pr_number: int = Field(description="PR number that brought this commit to this environment")

class Commit(BaseModel):
    sha: str = Field(description="Git commit SHA hash")
    message: str = Field(description="Commit message")
    author: str = Field(description="Author of the commit")
    timestamp: datetime = Field(description="ISO timestamp when commit was created")
    promotion_path: List[CommitPromotionPath] = Field(description="Sequential path showing promotion through environments")

class InitialPR(BaseModel):
    number: int = Field(description="PR number of the initial PR")
    title: str = Field(description="Title of the initial PR")
    created_at: Optional[datetime] = Field(None, description="ISO timestamp when PR was created")
    merged_at: datetime = Field(description="ISO timestamp when PR was merged")
    commits: List[Commit] = Field(description="List of commits in this PR")

class EnvironmentTransition(BaseModel):
    commit_sha: str = Field(description="Git commit SHA hash")
    pr_number: int = Field(description="PR number that brought this commit to this environment")
    entry_timestamp: datetime = Field(description="ISO timestamp when commit entered this environment")

class Environment(BaseModel):
    name: str = Field(description="Environment name (e.g., develop, UAT, staging, main)")
    transitions: List[EnvironmentTransition] = Field(description="List of commit transitions into this environment")

class Build(BaseModel):
    id: str = Field(description="Unique build identifier")
    commit_sha: str = Field(description="Git commit SHA hash built")
    environment: str = Field(description="Environment where build ran")
    status: str = Field(description="Build status (SUCCESS, FAILURE, ABORTED)")
    start_timestamp: datetime = Field(description="ISO timestamp when build started")
    end_timestamp: datetime = Field(description="ISO timestamp when build ended")
    duration_seconds: float = Field(description="Build duration in seconds")

class Artifact(BaseModel):
    uri: str = Field(description="Artifact URI in JFrog Artifactory")
    repo: str = Field(description="Repository name where artifact is stored")
    path: str = Field(description="Artifact path within the repository")
    commit_sha: str = Field(description="Git commit SHA hash associated with artifact")
    checksums: dict = Field(description="Artifact checksums (SHA1, SHA256, etc.)")
    properties: dict = Field(description="Artifact properties (build.number, commit.sha, etc.)")
    created_timestamp: Optional[datetime] = Field(None, description="ISO timestamp when artifact was created")
    size_bytes: Optional[int] = Field(None, description="Artifact size in bytes")

class PromotionPR(BaseModel):
    pr_number: int = Field(description="PR number used for promotion")
    from_environment: str = Field(description="Source environment")
    to_environment: str = Field(description="Target environment")
    merged_at: datetime = Field(description="ISO timestamp when PR was merged")

class PromotionChain(BaseModel):
    initial_commit: str = Field(description="Git commit SHA hash of the initial commit")
    promotion_prs: List[PromotionPR] = Field(description="Sequence of PRs used to promote this commit")
    final_production_timestamp: datetime = Field(description="ISO timestamp when commit reached production")

class Failure(BaseModel):
    build_id: str = Field(description="Unique build identifier that failed")
    commit_sha: str = Field(description="Git commit SHA hash that failed")
    environment: str = Field(description="Environment where failure occurred")
    timestamp: datetime = Field(description="ISO timestamp of failure")
    recovery_build_id: Optional[str] = Field(None, description="Build ID that recovered from failure")
    recovery_timestamp: Optional[datetime] = Field(None, description="ISO timestamp when failure was recovered")

class Metadata(BaseModel):
    data_collection_timestamp: datetime = Field(description="ISO timestamp when this data was collected")
    total_commits_analyzed: int = Field(description="Total number of commits analyzed")
    total_prs_analyzed: int = Field(description="Total number of PRs analyzed")
    total_builds_analyzed: int = Field(description="Total number of builds analyzed")
    total_artifacts_analyzed: int = Field(description="Total number of artifacts analyzed")
    environments_covered: List[str] = Field(description="List of environments covered in analysis")

# Main output schema
class DORAMetricsOutput(BaseModel):
    repository: str = Field(description="Repository name in owner/repo format")
    initial_pr: InitialPR = Field(description="Information about the initial PR")
    environments: List[Environment] = Field(description="List of environments and their transitions")
    builds: List[Build] = Field(description="List of all builds related to analyzed commits")
    artifacts: List[Artifact] = Field(description="List of all artifacts related to analyzed commits")
    promotion_chains: List[PromotionChain] = Field(description="Chains showing how commits were promoted")
    failures: List[Failure] = Field(description="List of build failures")
    metadata: Metadata = Field(description="Summary metadata about the collected data")

# Custom JSON encoder to handle datetime objects
class DateTimeEncoder(json.JSONEncoder):
    def default(self, obj):
        if isinstance(obj, datetime):
            return obj.isoformat()
        return super().default(obj)

# --- Tool connection functions ---

async def get_github_tools_async():
    """Connects to the GitHub MCP server and returns the tools and exit stack."""
    log_sequential_event("INFO", "Attempting to connect to GitHub MCP server")
    try:
        tools, exit_stack = await MCPToolset.from_server(
            connection_params=StdioServerParameters(
                command='python',
                args=['sequential_agent/github-mcp-server/main.py'],
                env={
                    "GITHUB_PERSONAL_ACCESS_TOKEN": GITHUB_TOKEN
                }
            )
        )

        sequential_agent_state.github_agent_state["connection_status"] = "connected"
        sequential_agent_state.github_agent_state["tools_discovered"] = len(tools)

        log_sequential_event("INFO", "Successfully connected to GitHub server",
                           tools_discovered=len(tools))
        for tool in tools:
            log_sequential_event("DEBUG", "Discovered GitHub tool", tool_name=tool.name)
        return tools, exit_stack
    except Exception as e:
        sequential_agent_state.github_agent_state["connection_status"] = "failed"
        sequential_agent_state.errors_count += 1

        log_sequential_event("ERROR", "Failed to connect to GitHub server", error=str(e))
        class DummyExitStack:
            async def __aenter__(self): return self
            async def __aexit__(self, *args): pass
        return [], DummyExitStack()

async def get_jenkins_tools_async():
    """Connects to multiple Jenkins MCP servers and returns the tools and exit stack."""
    log_sequential_event("INFO", "Attempting to connect to multiple Jenkins MCP servers")
    try:
        # Prepare environment variables for all Jenkins instances
        jenkins_env = {
            # Development Jenkins (qa-dev)
            "JENKINS_DEV_URL": JENKINS_DEV_URL,
            "JENKINS_DEV_USERNAME": JENKINS_DEV_USERNAME,
            "JENKINS_DEV_PASSWORD": JENKINS_DEV_PASSWORD,

            # UAT/SIT Jenkins (qa-sit)
            "JENKINS_UAT_URL": JENKINS_UAT_URL,
            "JENKINS_UAT_USERNAME": JENKINS_UAT_USERNAME,
            "JENKINS_UAT_PASSWORD": JENKINS_UAT_PASSWORD,

            # Production Jenkins (qa-stage, qa-prod)
            "JENKINS_PROD_URL": JENKINS_PROD_URL,
            "JENKINS_PROD_USERNAME": JENKINS_PROD_USERNAME,
            "JENKINS_PROD_PASSWORD": JENKINS_PROD_PASSWORD,

            # Legacy Jenkins (backward compatibility)
            "JENKINS_URL": JENKINS_URL,
            "JENKINS_USERNAME": JENKINS_USERNAME,
            "JENKINS_API_TOKEN": JENKINS_API_TOKEN,

            # DORA Environment Configuration
            "DORA_ENVIRONMENTS": ",".join(DORA_ENVIRONMENTS),
            "DORA_ENVIRONMENT_MAPPING": ",".join([f"{k}:{v}" for k, v in DORA_ENVIRONMENT_MAPPING.items()])
        }

        tools, exit_stack = await MCPToolset.from_server(
            connection_params=StdioServerParameters(
                command='python',
                args=['sequential_agent/jenkins-mcp-server/main.py'],
                env=jenkins_env
            )
        )

        sequential_agent_state.jenkins_agent_state["connection_status"] = "connected"
        sequential_agent_state.jenkins_agent_state["tools_discovered"] = len(tools)
        sequential_agent_state.jenkins_agent_state["jenkins_instances"] = {
            "dev": JENKINS_DEV_URL,
            "uat": JENKINS_UAT_URL,
            "prod": JENKINS_PROD_URL
        }

        log_sequential_event("INFO", "Successfully connected to Jenkins servers",
                           tools_discovered=len(tools),
                           jenkins_instances=3)
        for tool in tools:
            log_sequential_event("DEBUG", "Discovered Jenkins tool", tool_name=tool.name)
        return tools, exit_stack
    except Exception as e:
        sequential_agent_state.jenkins_agent_state["connection_status"] = "failed"
        sequential_agent_state.errors_count += 1

        log_sequential_event("ERROR", "Failed to connect to Jenkins servers", error=str(e))
        class DummyExitStack:
            async def __aenter__(self): return self
            async def __aexit__(self, *args): pass
        return [], DummyExitStack()

async def get_jfrog_tools_async():
    """Connects to the JFrog MCP server and returns the tools and exit stack."""
    log_sequential_event("INFO", "Attempting to connect to JFrog MCP server")
    try:
        # Prepare environment variables for JFrog with artifact paths
        jfrog_env = {
            "JFROG_URL": JFROG_URL,
            "JFROG_USERNAME": JFROG_USERNAME,
            "JFROG_PASSWORD": JFROG_PASSWORD,
            "JFROG_ACCESS_TOKEN": JFROG_ACCESS_TOKEN,

            # Artifact paths by environment
            "JFROG_DEV_PATH": JFROG_DEV_PATH,
            "JFROG_SIT_PATH": JFROG_SIT_PATH,
            "JFROG_STAGE_PATH": JFROG_STAGE_PATH,
            "JFROG_PROD_PATH": JFROG_PROD_PATH,

            # DORA Environment Configuration
            "DORA_ENVIRONMENTS": ",".join(DORA_ENVIRONMENTS),
            "DORA_ENVIRONMENT_MAPPING": ",".join([f"{k}:{v}" for k, v in DORA_ENVIRONMENT_MAPPING.items()])
        }

        tools, exit_stack = await MCPToolset.from_server(
            connection_params=StdioServerParameters(
                command='python',
                args=['sequential_agent/jfrog-mcp-server/main.py'],
                env=jfrog_env
            )
        )

        sequential_agent_state.jfrog_agent_state["connection_status"] = "connected"
        sequential_agent_state.jfrog_agent_state["tools_discovered"] = len(tools)
        sequential_agent_state.jfrog_agent_state["artifact_paths"] = {
            "dev": JFROG_DEV_PATH,
            "sit": JFROG_SIT_PATH,
            "stage": JFROG_STAGE_PATH,
            "prod": JFROG_PROD_PATH
        }

        log_sequential_event("INFO", "Successfully connected to JFrog server",
                           tools_discovered=len(tools),
                           artifact_paths=4)
        for tool in tools:
            log_sequential_event("DEBUG", "Discovered JFrog tool", tool_name=tool.name)
        return tools, exit_stack
    except Exception as e:
        sequential_agent_state.jfrog_agent_state["connection_status"] = "failed"
        sequential_agent_state.errors_count += 1

        log_sequential_event("ERROR", "Failed to connect to JFrog server", error=str(e))
        class DummyExitStack:
            async def __aenter__(self): return self
            async def __aexit__(self, *args): pass
        return [], DummyExitStack()

async def create_github_agent():
    """Creates the GitHub agent with appropriate tools."""
    github_tools, github_exit_stack = await get_github_tools_async()
    if not github_tools:
        print("--- WARNING: No GitHub tools discovered from MCP server. Agent will lack GitHub functionality. ---")

    github_agent = LlmAgent(
        name="GitHubPRAnalysisAgent",
        model=GEMINI_MODEL,
        instruction="""You are the GitHub PR Tracking Agent specialized in DORA Metrics analysis. Your primary mission is to track code changes across environments to enable comprehensive DORA metrics calculation.

## DORA Metrics Overview:
DORA (DevOps Research and Assessment) metrics are the four key metrics that indicate the performance of a software development team:

1. **Lead Time for Changes**: Time from code committed to code successfully running in production
2. **Deployment Frequency**: How often an organization successfully releases to production
3. **Mean Time to Recovery (MTTR)**: How long it takes to recover from a failure in production
4. **Change Failure Rate**: Percentage of deployments causing a failure in production

## Environment Flow Understanding:
Your system tracks changes through this specific environment progression:
- **qa-dev** (Development) → **qa-sit** (System Integration Testing) → **qa-stage** (Staging) → **qa-prod** (Production)

## Your Core Responsibilities:

### 1. **Lead Time for Changes Tracking:**
When given a PR number and repository name, you will:

**A. Collect Initial PR Information:**
   - You will always be given a PR that is merged into the main branch
   - Use `find_pr_commits` to identify all commits in the specified PR
   - Extract PR details including base branch, head branch, PR status, merge timestamp
   - Record the initial commit timestamp (start of lead time measurement)

**B. Trace Commit Progression Through Environments:**
   - Use `trace_commit_path` to track ONLY the LATEST commit's journey through branches
   - Map branch progression to environment flow: qa-dev → qa-sit → qa-stage → qa-prod
   - Document precise timestamps for each environment transition
   - Calculate time spent in each environment stage
   - Determine if commits have reached production (completion of lead time)

**C. Identify and Analyze ALL Promotion PRs:**
   - Find ALL intermediate PRs used to promote commits between environments
   - Trace the base commit's journey through branches to identify promotion PRs and squash PRs
   - For EACH intermediate PR identified, use `find_pr_commits` to get ALL commits included
   - This is critical: You MUST recursively collect commits from ALL PRs in the promotion chain
   - Analyze how commits were combined, squashed, or modified during promotion

**D. Calculate Lead Time Components:**
   - **Development Time**: Time from first commit to PR merge
   - **Integration Time**: Time spent in qa-dev environment
   - **Testing Time**: Time spent in qa-sit environment
   - **Staging Time**: Time spent in qa-stage environment
   - **Production Deployment Time**: Time to reach qa-prod
   - **Total Lead Time**: End-to-end time from first commit to production

### 2. **Deployment Frequency Support:**
   - Track all commits that reach production within specified time periods
   - Document deployment patterns and frequencies
   - Identify batch vs. continuous deployment patterns

### 3. **Change Failure Rate Preparation:**
   - Document all commits and their promotion paths for failure correlation
   - Track which commits were reverted or caused rollbacks
   - Prepare data for downstream failure analysis

### 4. **Performance Analysis and Recommendations:**
Based on your analysis, provide insights on:
   - **Bottlenecks**: Which environment stages take longest?
   - **Optimization Opportunities**: Where can lead time be reduced?
   - **Process Improvements**: Suggestions for faster, more reliable deployments
   - **Risk Factors**: Patterns that might indicate higher failure risk

### 5. **Compile Comprehensive Results:**
Provide a well-structured JSON with complete information about:
   - Initial PR and its commits with timestamps
   - All intermediate PRs and their commits
   - Full promotion path of every commit across environments
   - Detailed timestamp data for all environment transitions
   - Lead time calculations for each stage
   - Complete chain of PRs involved in the promotion process
   - Performance insights and recommendations

## Key Success Metrics:
- **Accuracy**: Precise timestamp tracking for lead time calculation
- **Completeness**: All commits in promotion chain captured
- **Traceability**: Clear path from development to production
- **Actionability**: Insights that enable process improvement

Your output MUST be a comprehensive JSON containing all collected data about every commit and PR involved in the entire promotion chain, with precise timing data to enable accurate DORA metrics calculation and actionable insights for development process optimization.
""",
        description="Analyzes GitHub PRs and traces commits across environments",
        tools=github_tools,
        output_key="github_data"
    )

    return github_agent, github_exit_stack

async def create_jenkins_agent():
    """Creates the Jenkins agent with appropriate tools."""
    jenkins_tools, jenkins_exit_stack = await get_jenkins_tools_async()
    if not jenkins_tools:
        print("--- WARNING: No Jenkins tools discovered from MCP server. Agent will lack Jenkins functionality. ---")

    jenkins_agent = LlmAgent(
        name="JenkinsBuildAnalysisAgent",
        model=GEMINI_MODEL,
        instruction="""You are the Jenkins Build Analysis Agent specialized in DORA Metrics tracking across multiple Jenkins instances. Your mission is to track build and deployment information to support comprehensive DORA metrics calculation.

## DORA Metrics Context:
You contribute to all four DORA metrics by tracking build and deployment data:

1. **Lead Time for Changes**: Track build times and deployment durations across environments
2. **Deployment Frequency**: Monitor how often builds are deployed to production
3. **Mean Time to Recovery (MTTR)**: Track build failures and recovery times
4. **Change Failure Rate**: Identify failed builds and their impact on deployments

## Multi-Jenkins Environment Architecture:
You have access to multiple Jenkins instances mapping to specific environments:

- **Development Jenkins** (jenkins.truxt.ai) → **qa-dev** environment
- **UAT/SIT Jenkins** (jenkins2.truxt.ai) → **qa-sit** environment
- **Production Jenkins** (jenkins3.truxt.ai) → **qa-stage** and **qa-prod** environments

## Environment Flow Mapping:
- **qa-dev** (Development) → **qa-sit** (System Integration Testing) → **qa-stage** (Staging) → **qa-prod** (Production)

**Input**: You will receive a comprehensive JSON with GitHub PR and commit data:
```
{github_data}
```

## Your Core Responsibilities:

### 1. **Multi-Instance Build Tracking:**
For EACH commit SHA in the input data:

**A. Search Across All Jenkins Instances:**
   - Use `get_build_by_commit_hash` to locate builds in Development Jenkins (qa-dev)
   - Use `get_build_by_commit_hash` to locate builds in UAT Jenkins (qa-sit)
   - Use `get_build_by_commit_hash` to locate builds in Production Jenkins (qa-stage, qa-prod)
   - Map builds to the correct environment based on Jenkins instance

**B. Collect Comprehensive Build Details:**
   - Build number, status (SUCCESS, FAILURE, UNSTABLE), duration
   - Build start timestamp, completion time, and total duration
   - Environment deployed to (qa-dev, qa-sit, qa-stage, qa-prod)
   - Build artifacts, deployment status, and deployment duration
   - Build parameters and triggered by information

### 2. **Lead Time Component Calculation:**
   - **Build Time**: Time taken for each build in each environment
   - **Queue Time**: Time builds spent waiting in queue
   - **Deployment Time**: Time taken to deploy to each environment
   - **Environment Transition Time**: Time between environment deployments

### 3. **Environment Progression Tracking:**
   - Map builds to the environment flow: qa-dev → qa-sit → qa-stage → qa-prod
   - Document the progression of commits through build pipelines
   - Identify bottlenecks in the deployment pipeline
   - Track which commits successfully reach production

### 4. **Performance Analysis:**
   - **Build Performance**: Which environments have longest build times?
   - **Pipeline Bottlenecks**: Where do builds get stuck or delayed?
   - **Failure Patterns**: Which environments have highest failure rates?
   - **Deployment Frequency**: How often builds reach production?

### 5. **Compile Results:**
Return a JSON with all build information linked to the corresponding commits and PRs:
   - Include build timestamps, durations, and status data for each environment
   - Maintain the relationship between commits, PRs, and their builds
   - Include performance metrics and bottleneck analysis
   - Provide insights for DORA metrics calculation

Your output MUST be a well-structured JSON containing all collected data about builds linked to the GitHub commits across all Jenkins instances, with precise timing data to enable accurate DORA metrics calculation.
""",
        description="Analyzes Jenkins builds for commits identified by the GitHub agent",
        tools=jenkins_tools,
        output_key="jenkins_data"
    )

    return jenkins_agent, jenkins_exit_stack

async def create_jfrog_agent():
    """Creates the JFrog agent with appropriate tools."""
    jfrog_tools, jfrog_exit_stack = await get_jfrog_tools_async()
    if not jfrog_tools:
        print("--- WARNING: No JFrog tools discovered from MCP server. Agent will lack JFrog functionality. ---")

    jfrog_agent = LlmAgent(
        name="JFrogArtifactAnalysisAgent",
        model=GEMINI_MODEL,
        instruction="""You are the JFrog Artifact Analysis Agent specialized in DORA Metrics tracking across multiple environment artifact paths. Your mission is to track artifact deployment information to support comprehensive DORA metrics calculation.

## DORA Metrics Context:
You contribute to all four DORA metrics by tracking artifact deployment data:

1. **Lead Time for Changes**: Track artifact creation and deployment times across environments
2. **Deployment Frequency**: Monitor how often artifacts are deployed to production
3. **Mean Time to Recovery (MTTR)**: Track artifact rollbacks and recovery deployments
4. **Change Failure Rate**: Identify failed deployments and their artifact correlation

## Multi-Environment Artifact Architecture:
You have access to JFrog Artifactory with environment-specific artifact paths:

- **Development Artifacts**: `truxt12345678-repo-1/nextjs-builds/` → **qa-dev** environment
- **SIT Artifacts**: `sitCI/nextjs-builds/` → **qa-sit** environment
- **Stage Artifacts**: `stageCI/nextjs-builds/` → **qa-stage** environment
- **Production Artifacts**: `prodCI/nextjs-builds/` → **qa-prod** environment

## Environment Flow Mapping:
- **qa-dev** (Development) → **qa-sit** (System Integration Testing) → **qa-stage** (Staging) → **qa-prod** (Production)

**Input**: You will receive a comprehensive JSON with GitHub PR and commit data:
```
{github_data}
```

## Your Core Responsibilities:

### 1. **Multi-Environment Artifact Tracking:**
For EACH commit SHA in the input data:

**A. Search Across All Artifact Paths:**
   - Use `get_artifact_by_commit_sha` to locate artifacts in Development path (truxt12345678-repo-1/nextjs-builds/)
   - Use `get_artifact_by_commit_sha` to locate artifacts in SIT path (sitCI/nextjs-builds/)
   - Use `get_artifact_by_commit_sha` to locate artifacts in Stage path (stageCI/nextjs-builds/)
   - Use `get_artifact_by_commit_sha` to locate artifacts in Production path (prodCI/nextjs-builds/)
   - Map artifacts to the correct environment based on artifact path

**B. Collect Comprehensive Artifact Details:**
   - Artifact name, version, size, and checksums
   - Creation timestamp, upload time, and deployment metadata
   - Environment deployed to (qa-dev, qa-sit, qa-stage, qa-prod)
   - Build information from artifact properties (build.number, commit.sha, etc.)
   - Artifact promotion history and deployment status

### 2. **Lead Time Component Calculation:**
   - **Artifact Creation Time**: Time taken to build and upload artifacts
   - **Promotion Time**: Time between environment artifact deployments
   - **Deployment Time**: Time from artifact upload to environment deployment
   - **Environment Transition Time**: Time between environment artifact promotions

### 3. **Environment Progression Tracking:**
   - Map artifacts to the environment flow: qa-dev → qa-sit → qa-stage → qa-prod
   - Document the progression of artifacts through deployment pipelines
   - Identify bottlenecks in the artifact promotion pipeline
   - Track which artifacts successfully reach production

### 4. **Deployment Frequency Analysis:**
   - Track artifact deployment patterns across environments
   - Identify deployment frequency to production (prodCI path)
   - Monitor artifact promotion success rates
   - Calculate artifact deployment success rates

### 5. **Performance Analysis:**
   - **Artifact Performance**: Which environments have longest artifact creation times?
   - **Promotion Bottlenecks**: Where do artifacts get stuck or delayed?
   - **Deployment Patterns**: Which environments have highest deployment rates?
   - **Artifact Quality**: Success rates of artifact deployments?

### 6. **Artifact Properties Analysis:**
   - Extract build information from artifact properties (build.number, commit.sha, etc.)
   - Document artifact versions and deployment metadata
   - Correlate artifacts with Jenkins builds and GitHub commits
   - Identify any artifact deployment patterns or issues

### 7. **Compile Results:**
Return a JSON with all artifact information linked to the corresponding commits and PRs:
   - Include artifact timestamps, metadata, and deployment data for each environment
   - Maintain the relationship between commits, builds, and their artifacts
   - Include performance metrics and bottleneck analysis
   - Provide insights for DORA metrics calculation

Your output MUST be a well-structured JSON containing all collected data about artifacts linked to the GitHub commits across all environment paths, with precise timing data to enable accurate DORA metrics calculation and deployment tracking.
""",
        description="Analyzes JFrog artifacts for commits identified by the GitHub agent",
        tools=jfrog_tools,
        output_key="jfrog_data"
    )

    return jfrog_agent, jfrog_exit_stack

async def create_mapping_agent():
    """Creates the mapping agent for DORA metrics analysis."""
    mapping_agent = LlmAgent(
        name="DORAMetricsMapperAgent",
        model=GEMINI_MODEL,
        instruction="""You are the DORA Metrics Data Consolidation and Analysis Agent. Your mission is to combine GitHub, Jenkins, and JFrog data into a comprehensive DORA metrics analysis with actionable insights and recommendations.

## DORA Metrics Overview:
DORA (DevOps Research and Assessment) metrics are the four key metrics that indicate the performance of a software development team:

1. **Lead Time for Changes**: Time from code committed to code successfully running in production
2. **Deployment Frequency**: How often an organization successfully releases to production
3. **Mean Time to Recovery (MTTR)**: How long it takes to recover from a failure in production
4. **Change Failure Rate**: Percentage of deployments causing a failure in production

## Environment Flow Context:
- **qa-dev** (Development) → **qa-sit** (System Integration Testing) → **qa-stage** (Staging) → **qa-prod** (Production)

**Input**: You will receive GitHub PR tracing data, Jenkins build data, and JFrog artifact data:
```
GitHub Data:
{github_data}

Jenkins Data:
{jenkins_data}

JFrog Data:
{jfrog_data}
```

## Your Core Responsibilities:

### 1. **Data Consolidation and Correlation:**
   - Merge and normalize GitHub, Jenkins, and JFrog data into a consistent structure
   - Correlate commits across all three systems using commit SHAs
   - Create a complete timeline of each commit's journey from development to production
   - Ensure data integrity and completeness across all sources

### 2. **DORA Metrics Calculation:**

**A. Lead Time for Changes:**
   - Calculate total lead time from first commit to production deployment
   - Break down lead time by environment stages:
     - Development time (commit to qa-dev)
     - Integration time (qa-dev to qa-sit)
     - Testing time (qa-sit to qa-stage)
     - Production deployment time (qa-stage to qa-prod)
   - Identify bottlenecks and longest stages
   - Calculate average, median, and 95th percentile lead times

**B. Deployment Frequency:**
   - Count successful deployments to production within time periods
   - Calculate deployment frequency (daily, weekly, monthly)
   - Identify deployment patterns and trends
   - Compare deployment frequency across different time periods

**C. Mean Time to Recovery (MTTR):**
   - Identify failed builds and deployments
   - Calculate time from failure detection to successful recovery
   - Track rollback and hotfix deployment times
   - Analyze recovery patterns and improvement trends

**D. Change Failure Rate:**
   - Calculate percentage of deployments causing failures
   - Identify failure patterns across environments
   - Correlate failures with specific commits or changes
   - Track failure rate trends over time

### 3. **Performance Analysis and Insights:**

**A. Bottleneck Identification:**
   - Identify which environment stages take the longest
   - Highlight delays in build processes, testing, or deployments
   - Pinpoint specific commits or PRs causing delays
   - Analyze patterns in slow deployments

**B. Optimization Opportunities:**
   - Suggest areas for lead time reduction
   - Recommend process improvements for faster deployments
   - Identify automation opportunities
   - Highlight successful patterns to replicate

**C. Risk Assessment:**
   - Identify high-risk changes or patterns
   - Highlight commits with higher failure rates
   - Assess deployment stability across environments
   - Recommend risk mitigation strategies

### 4. **Actionable Recommendations:**

**A. Process Improvements:**
   - Specific suggestions for reducing lead time
   - Recommendations for increasing deployment frequency
   - Strategies for reducing failure rates
   - Process optimizations for faster recovery

**B. Technical Improvements:**
   - Infrastructure optimizations
   - Build process enhancements
   - Testing strategy improvements
   - Deployment pipeline optimizations

**C. Team and Organizational Improvements:**
   - Development workflow recommendations
   - Code review process improvements
   - Release management enhancements
   - Monitoring and alerting improvements

### 5. **Comprehensive Output:**
Format your response according to the exact schema provided, including:
   - Complete DORA metrics calculations with historical trends
   - Detailed performance analysis and bottleneck identification
   - Specific, actionable recommendations for improvement
   - Risk assessment and mitigation strategies
   - Success patterns and best practices identified

Your analysis should provide both high-level DORA metrics and granular insights that enable teams to understand their current performance and take specific actions to improve their software delivery capabilities.
""",
        description="Consolidates GitHub, Jenkins, and JFrog data into a standardized format for DORA metrics calculation",
        output_schema=DORAMetricsOutput,
        output_key="dora_metrics_data"
        # after_agent_callback=send_to_ingest_callback  # Disabled for chat interface
    )

    return mapping_agent, AsyncExitStack()  # Empty exit stack for mapping agent

def send_to_ingest_callback(callback_context: CallbackContext) -> Optional[LlmResponse]:
    """
    After-agent callback that sends the generated DORA metrics data to the ingest endpoint.

    Args:
        callback_context: Context containing information about the agent

    Returns:
        Optional[LlmResponse]: Returns None to keep the original response unchanged
    """
    print("--------------------------------")
    # Print the actual dictionary content, not just the object reference
    print("State dictionary contents:")
    agent_state = callback_context.state.to_dict()
    print(f"--- After-agent callback triggered for {callback_context.agent_name} ---")

    try:
        # Check if we have the data in state
        if "dora_metrics_data" not in agent_state:
            print("No DORA metrics data found in agent state")
            return None

        # Get the data from state - this is already a Python object, not a JSON string
        dora_metrics_data = agent_state["dora_metrics_data"]

        # Convert the Python object to a JSON string using the custom encoder for datetime objects
        ingest_endpoint = os.environ.get("INGEST_ENDPOINT", "http://52.233.88.40:3000/ingest")
        print(f"Sending DORA metrics data to ingest endpoint: {ingest_endpoint}")

        print(dora_metrics_data)



        # INGESTION DISABLED - For now, this is only a chatting interface
        # Send the data to the ingest endpoint
        # response = requests.post(
        #     ingest_endpoint,
        #     # Use the custom encoder to handle datetime objects
        #     data=json.dumps(dora_metrics_data, cls=DateTimeEncoder),
        #     headers={"Content-Type": "application/json"},
        #     timeout=30
        # )

        # # Check response
        # response.raise_for_status()
        # print(f"Successfully sent data to ingest endpoint. Status code: {response.status_code}")

        # # Try to get JSON response
        # try:
        #     result = response.json()
        #     print(f"Response: {json.dumps(result, indent=2)}")
        # except:
        #     print(f"Response: {response.text}")

        print("DORA metrics data processed successfully (ingestion disabled for chat interface)")

    except Exception as e:
        print(f"Error in send_to_ingest_callback: {str(e)}")

    #Return None to keep the original response unchanged
    return None

async def create_sequential_agent():
    """
    Creates the main sequential agent that orchestrates all sub-agents.

    Current Configuration (Chat Interface Only):
    - GitHub Agent: Tracks PRs and commits across environment branches
    - Jenkins Agent: Tracks builds across multiple Jenkins instances (dev, uat, prod)
    - JFrog Agent: Tracks artifacts across environment-specific paths
    - Mapping Agent: Consolidates data and provides DORA metrics analysis

    Note: Database agent is NOT included in this pipeline.
    Note: Data ingestion is DISABLED - this is for chat interface only.
    """
    log_sequential_event("INFO", "Creating sequential agent pipeline")

    exit_stack = AsyncExitStack()

    # Create GitHub agent
    github_agent, github_exit_stack = await create_github_agent()
    await exit_stack.enter_async_context(github_exit_stack)

    # Create Jenkins agent
    jenkins_agent, jenkins_exit_stack = await create_jenkins_agent()
    await exit_stack.enter_async_context(jenkins_exit_stack)

    # Create JFrog agent
    jfrog_agent, jfrog_exit_stack = await create_jfrog_agent()
    await exit_stack.enter_async_context(jfrog_exit_stack)

    # Create mapping agent (doesn't need tools, no ingestion)
    mapping_agent, _ = await create_mapping_agent()

    # NOTE: Database agent is NOT included in this pipeline
    # The database agent remains available as a standalone agent for separate analysis

    # Create sequential agent with enhanced logging and state management
    sequential_agent = SequentialAgent(
        name="DORAMetricsPipelineAgent",
        sub_agents=[github_agent, jenkins_agent, jfrog_agent, mapping_agent],
        description="Enhanced DORA metrics pipeline agent with comprehensive logging and state management that analyzes PR and build data to calculate DORA metrics",

        # Add callbacks for enhanced observability
        before_agent_callback=before_sequential_agent_callback,
        after_agent_callback=after_sequential_agent_callback
    )

    log_sequential_event("INFO", "Sequential agent pipeline created successfully",
                        sub_agents_count=len([github_agent, jenkins_agent, jfrog_agent, mapping_agent]))

    return sequential_agent, exit_stack

# For ADK tools compatibility, the root agent must be named `root_agent`
import asyncio

async def get_root_agent():
    """Get the root agent asynchronously"""
    return await create_sequential_agent()

# Create the root agent
try:
    root_agent = asyncio.run(get_root_agent())
except RuntimeError:
    # If we're already in an async context, create a task
    root_agent = create_sequential_agent()
