import asyncio
import os
from contextlib import AsyncExitStack
from google.adk.agents import Agent, SequentialAgent, LlmAgent
from google.adk.tools.mcp_tool.mcp_toolset import MCPToolset, StdioServerParameters
from dotenv import load_dotenv
from pydantic import BaseModel, Field
from typing import List, Optional, Any
from datetime import datetime
from google.adk.agents.callback_context import CallbackContext
from google.adk.models import LlmResponse
import requests
import json

# Load environment variables
load_dotenv(dotenv_path=os.path.join(os.path.dirname(__file__), '..', '..', '.env'))

# Constants
GITHUB_TOKEN = os.getenv("GITHUB_PERSONAL_ACCESS_TOKEN")
JENKINS_URL = os.getenv("JENKINS_URL")
JENKINS_USERNAME = os.getenv("JENKINS_USERNAME")
JENKINS_API_TOKEN = os.getenv("JENKINS_API_TOKEN")
JFROG_URL = os.getenv("JFROG_URL")
JFROG_USERNAME = os.getenv("JFROG_USERNAME")
JFROG_PASSWORD = os.getenv("JFROG_PASSWORD")
GEMINI_MODEL = "gemini-2.5-flash-preview-04-17"  # Ensure API key is in .env

# Nested models
class CommitPromotionPath(BaseModel):
    environment: str = Field(description="The environment name (e.g., develop, UAT, staging, main)")
    timestamp: datetime = Field(description="ISO timestamp when the commit entered this environment")
    pr_number: int = Field(description="PR number that brought this commit to this environment")

class Commit(BaseModel):
    sha: str = Field(description="Git commit SHA hash")
    message: str = Field(description="Commit message")
    author: str = Field(description="Author of the commit")
    timestamp: datetime = Field(description="ISO timestamp when commit was created")
    promotion_path: List[CommitPromotionPath] = Field(description="Sequential path showing promotion through environments")

class InitialPR(BaseModel):
    number: int = Field(description="PR number of the initial PR")
    title: str = Field(description="Title of the initial PR")
    created_at: Optional[datetime] = Field(None, description="ISO timestamp when PR was created")
    merged_at: datetime = Field(description="ISO timestamp when PR was merged")
    commits: List[Commit] = Field(description="List of commits in this PR")

class EnvironmentTransition(BaseModel):
    commit_sha: str = Field(description="Git commit SHA hash")
    pr_number: int = Field(description="PR number that brought this commit to this environment")
    entry_timestamp: datetime = Field(description="ISO timestamp when commit entered this environment")

class Environment(BaseModel):
    name: str = Field(description="Environment name (e.g., develop, UAT, staging, main)")
    transitions: List[EnvironmentTransition] = Field(description="List of commit transitions into this environment")

class Build(BaseModel):
    id: str = Field(description="Unique build identifier")
    commit_sha: str = Field(description="Git commit SHA hash built")
    environment: str = Field(description="Environment where build ran")
    status: str = Field(description="Build status (SUCCESS, FAILURE, ABORTED)")
    start_timestamp: datetime = Field(description="ISO timestamp when build started")
    end_timestamp: datetime = Field(description="ISO timestamp when build ended")
    duration_seconds: float = Field(description="Build duration in seconds")

class Artifact(BaseModel):
    uri: str = Field(description="Artifact URI in JFrog Artifactory")
    repo: str = Field(description="Repository name where artifact is stored")
    path: str = Field(description="Artifact path within the repository")
    commit_sha: str = Field(description="Git commit SHA hash associated with artifact")
    checksums: dict = Field(description="Artifact checksums (SHA1, SHA256, etc.)")
    properties: dict = Field(description="Artifact properties (build.number, commit.sha, etc.)")
    created_timestamp: Optional[datetime] = Field(None, description="ISO timestamp when artifact was created")
    size_bytes: Optional[int] = Field(None, description="Artifact size in bytes")

class PromotionPR(BaseModel):
    pr_number: int = Field(description="PR number used for promotion")
    from_environment: str = Field(description="Source environment")
    to_environment: str = Field(description="Target environment")
    merged_at: datetime = Field(description="ISO timestamp when PR was merged")

class PromotionChain(BaseModel):
    initial_commit: str = Field(description="Git commit SHA hash of the initial commit")
    promotion_prs: List[PromotionPR] = Field(description="Sequence of PRs used to promote this commit")
    final_production_timestamp: datetime = Field(description="ISO timestamp when commit reached production")

class Failure(BaseModel):
    build_id: str = Field(description="Unique build identifier that failed")
    commit_sha: str = Field(description="Git commit SHA hash that failed")
    environment: str = Field(description="Environment where failure occurred")
    timestamp: datetime = Field(description="ISO timestamp of failure")
    recovery_build_id: Optional[str] = Field(None, description="Build ID that recovered from failure")
    recovery_timestamp: Optional[datetime] = Field(None, description="ISO timestamp when failure was recovered")

class Metadata(BaseModel):
    data_collection_timestamp: datetime = Field(description="ISO timestamp when this data was collected")
    total_commits_analyzed: int = Field(description="Total number of commits analyzed")
    total_prs_analyzed: int = Field(description="Total number of PRs analyzed")
    total_builds_analyzed: int = Field(description="Total number of builds analyzed")
    total_artifacts_analyzed: int = Field(description="Total number of artifacts analyzed")
    environments_covered: List[str] = Field(description="List of environments covered in analysis")

# Main output schema
class DORAMetricsOutput(BaseModel):
    repository: str = Field(description="Repository name in owner/repo format")
    initial_pr: InitialPR = Field(description="Information about the initial PR")
    environments: List[Environment] = Field(description="List of environments and their transitions")
    builds: List[Build] = Field(description="List of all builds related to analyzed commits")
    artifacts: List[Artifact] = Field(description="List of all artifacts related to analyzed commits")
    promotion_chains: List[PromotionChain] = Field(description="Chains showing how commits were promoted")
    failures: List[Failure] = Field(description="List of build failures")
    metadata: Metadata = Field(description="Summary metadata about the collected data")

# Custom JSON encoder to handle datetime objects
class DateTimeEncoder(json.JSONEncoder):
    def default(self, obj):
        if isinstance(obj, datetime):
            return obj.isoformat()
        return super().default(obj)

# --- Tool connection functions ---

async def get_github_tools_async():
    """Connects to the GitHub MCP server and returns the tools and exit stack."""
    print("--- Attempting to connect to GitHub MCP server ---")
    try:
        tools, exit_stack = await MCPToolset.from_server(
            connection_params=StdioServerParameters(
                command='python',
                args=['sequential_agent/github-mcp-server/main.py'],
                env={
                    "GITHUB_PERSONAL_ACCESS_TOKEN": GITHUB_TOKEN
                }
            )
        )

        print(f"--- Successfully connected to GitHub server. Discovered {len(tools)} tool(s). ---")
        for tool in tools:
            print(f"  - Discovered GitHub tool: {tool.name}")
        return tools, exit_stack
    except Exception as e:
        print(f"--- ERROR connecting to GitHub server: {e} ---")
        class DummyExitStack:
            async def __aenter__(self): return self
            async def __aexit__(self, *args): pass
        return [], DummyExitStack()

async def get_jenkins_tools_async():
    """Connects to the Jenkins MCP server and returns the tools and exit stack."""
    print("--- Attempting to connect to Jenkins MCP server ---")
    try:
        tools, exit_stack = await MCPToolset.from_server(
            connection_params=StdioServerParameters(
                command='python',
                args=['sequential_agent/jenkins-mcp-server/main.py'],
                env={
                    "JENKINS_URL": JENKINS_URL,
                    "JENKINS_USERNAME": JENKINS_USERNAME,
                    "JENKINS_API_TOKEN": JENKINS_API_TOKEN
                }
            )
        )

        print(f"--- Successfully connected to Jenkins server. Discovered {len(tools)} tool(s). ---")
        for tool in tools:
            print(f"  - Discovered Jenkins tool: {tool.name}")
        return tools, exit_stack
    except Exception as e:
        print(f"--- ERROR connecting to Jenkins server: {e} ---")
        class DummyExitStack:
            async def __aenter__(self): return self
            async def __aexit__(self, *args): pass
        return [], DummyExitStack()

async def get_jfrog_tools_async():
    """Connects to the JFrog MCP server and returns the tools and exit stack."""
    print("--- Attempting to connect to JFrog MCP server ---")
    try:
        tools, exit_stack = await MCPToolset.from_server(
            connection_params=StdioServerParameters(
                command='python',
                args=['sequential_agent/jfrog-mcp-server/main.py'],
                env={
                    "JFROG_URL": JFROG_URL,
                    "JFROG_USERNAME": JFROG_USERNAME,
                    "JFROG_PASSWORD": JFROG_PASSWORD
                }
            )
        )

        print(f"--- Successfully connected to JFrog server. Discovered {len(tools)} tool(s). ---")
        for tool in tools:
            print(f"  - Discovered JFrog tool: {tool.name}")
        return tools, exit_stack
    except Exception as e:
        print(f"--- ERROR connecting to JFrog server: {e} ---")
        class DummyExitStack:
            async def __aenter__(self): return self
            async def __aexit__(self, *args): pass
        return [], DummyExitStack()

async def create_github_agent():
    """Creates the GitHub agent with appropriate tools."""
    github_tools, github_exit_stack = await get_github_tools_async()
    if not github_tools:
        print("--- WARNING: No GitHub tools discovered from MCP server. Agent will lack GitHub functionality. ---")
    
    github_agent = LlmAgent(
        name="GitHubPRAnalysisAgent",
        model=GEMINI_MODEL,
        instruction="""You are the GitHub PRTracking Agent. Your task is to track code changes across environments using GitHub tools.

When given a PR number and repository name, you will:

1. **Collect Initial PR Information:**
   - You will be always given a PR that is merged into the main branch
   - Use `find_pr_commits` to identify all commits in the specified PR
   - Extract PR details including base branch, head branch, PR status

2. **Trace Commit Progression Through Environments:**
   - Use `trace_commit_path` to track ONLY the LATEST commit's journey through branches from the initial PR
   - Identify which environments that commit has reached (develop → UAT → staging → main)
   - Determine whether that commits have reached production (main branch)
   - Document that commit's promotion path step by step

3. **Identify and Analyze ALL Promotion PRs:**
   - Find ALL intermediate PRs that were used to promote these commits between environments
   - Trace the base commit's journey through branches to identify all intermediate PRs and squash PRs.
   - For EACH intermediate PR identified, use `find_pr_commits` again to get ALL commits included in that PR
   - This is critical: You MUST recursively collect commits from ALL PRs in the promotion chain
   - Analyze how commits were combined, squashed, or modified during promotion

4. **Collect Detailed Information for ALL Commits:**
   - Use `get_commit_details` for EACH commit (original PR commits and ALL intermediate PR commits)
   - Gather complete metadata for every commit in the entire promotion process
   - Track timestamps at each environment transition for ALL commits

5. **Compile Comprehensive Results:**
   - Provide a well-structured JSON with complete information about:
     - Initial PR and its commits
     - All intermediate PRs and their commits
     - Full promotion path of every commit across environments
     - Timestamp data for all environment transitions
     - Complete chain of PRs involved in the promotion process

Your output MUST be a comprehensive JSON containing all collected data about every commit and PR involved in the entire promotion chain, from initial development to production.
""",
        description="Analyzes GitHub PRs and traces commits across environments",
        tools=github_tools,
        output_key="github_data"
    )
    
    return github_agent, github_exit_stack

async def create_jenkins_agent():
    """Creates the Jenkins agent with appropriate tools."""
    jenkins_tools, jenkins_exit_stack = await get_jenkins_tools_async()
    if not jenkins_tools:
        print("--- WARNING: No Jenkins tools discovered from MCP server. Agent will lack Jenkins functionality. ---")
    
    jenkins_agent = LlmAgent(
        name="JenkinsBuildAnalysisAgent",
        model=GEMINI_MODEL,
        instruction="""You are the Jenkins BuildTracking Agent. Your task is to analyze Jenkins builds for each commit identified by the GitHub agent.

**Input**: You will receive a comprehensive JSON with GitHub PR and commit data:
```
{github_data}
```

Your tasks:

1. **Find Build Information:**
   - For EACH commit SHA in the input data, use `get_build_by_commit_hash` to locate all Jenkins builds
   - Collect complete build details including status, duration, timestamp, and environment

2. **Track Deployment Pipeline:**
   - Identify builds across different environments (dev, test, staging, prod)
   - Determine which environments each commit has been built and deployed to
   - Calculate time spent in each stage of the pipeline

3. **Analyze Build Performance:**
   - Track build durations and success/failure rates
   - Document deployment frequencies
   - Identify any build failures or delays

4. **Compile Results:**
   - Return a JSON with all build information linked to the corresponding commits and PRs
   - Include build timestamps, durations, and status data
   - Maintain the relationship between commits, PRs, and their builds

Your output MUST be a well-structured JSON containing all collected data about builds linked to the GitHub commits.
""",
        description="Analyzes Jenkins builds for commits identified by the GitHub agent",
        tools=jenkins_tools,
        output_key="jenkins_data"
    )
    
    return jenkins_agent, jenkins_exit_stack

async def create_jfrog_agent():
    """Creates the JFrog agent with appropriate tools."""
    jfrog_tools, jfrog_exit_stack = await get_jfrog_tools_async()
    if not jfrog_tools:
        print("--- WARNING: No JFrog tools discovered from MCP server. Agent will lack JFrog functionality. ---")
    
    jfrog_agent = LlmAgent(
        name="JFrogArtifactAnalysisAgent",
        model=GEMINI_MODEL,
        instruction="""You are the JFrog Artifact Tracking Agent. Your task is to analyze JFrog Artifactory artifacts for each commit identified by the GitHub agent.

**Input**: You will receive a comprehensive JSON with GitHub PR and commit data:
```
{github_data}
```

Your tasks:

1. **Find Artifact Information:**
   - For EACH commit SHA in the input data, use `get_artifact_by_commit_sha` to locate all JFrog artifacts
   - Collect complete artifact details including metadata, checksums, and properties
   - Document artifact creation and deployment information

2. **Track Artifact Deployment Pipeline:**
   - Identify artifacts across different repositories and environments
   - Determine which artifacts were created for each commit
   - Track artifact promotion through environments

3. **Analyze Artifact Properties:**
   - Extract build information from artifact properties (build.number, commit.sha, etc.)
   - Document artifact versions and deployment metadata
   - Identify any artifact deployment patterns or issues

4. **Compile Results:**
   - Return a JSON with all artifact information linked to the corresponding commits and PRs
   - Include artifact timestamps, metadata, and deployment data
   - Maintain the relationship between commits, builds, and their artifacts

Your output MUST be a well-structured JSON containing all collected data about artifacts linked to the GitHub commits.
""",
        description="Analyzes JFrog artifacts for commits identified by the GitHub agent",
        tools=jfrog_tools,
        output_key="jfrog_data"
    )
    
    return jfrog_agent, jfrog_exit_stack

async def create_mapping_agent():
    """Creates the mapping agent for DORA metrics analysis."""
    mapping_agent = LlmAgent(
        name="DORAMetricsMapperAgent",
        model=GEMINI_MODEL,
        instruction="""You are the DORA Metrics Data Consolidation Agent. Your task is to combine GitHub, Jenkins, and JFrog data into a standardized format for DORA metrics calculation.

**Input**: You will receive GitHub PR tracing data, Jenkins build data, and JFrog artifact data:
```
GitHub Data:
{github_data}

Jenkins Data:
{jenkins_data}

JFrog Data:
{jfrog_data}
```

Your task is to merge and normalize this data into a SINGLE, consistent JSON structure without performing any calculations. Format your response according to the exact schema provided.
""",
        description="Consolidates GitHub, Jenkins, and JFrog data into a standardized format for DORA metrics calculation",
        output_schema=DORAMetricsOutput,
        output_key="dora_metrics_data",
        after_agent_callback=send_to_ingest_callback
    )
    
    return mapping_agent, AsyncExitStack()  # Empty exit stack for mapping agent

def send_to_ingest_callback(callback_context: CallbackContext) -> Optional[LlmResponse]:
    """
    After-agent callback that sends the generated DORA metrics data to the ingest endpoint.
    
    Args:
        callback_context: Context containing information about the agent
        
    Returns:
        Optional[LlmResponse]: Returns None to keep the original response unchanged
    """
    print("--------------------------------")
    # Print the actual dictionary content, not just the object reference
    print("State dictionary contents:")
    agent_state = callback_context.state.to_dict()
    print(f"--- After-agent callback triggered for {callback_context.agent_name} ---")
    
    try:
        # Check if we have the data in state
        if "dora_metrics_data" not in agent_state:
            print("No DORA metrics data found in agent state")
            return None
            
        # Get the data from state - this is already a Python object, not a JSON string
        dora_metrics_data = agent_state["dora_metrics_data"]
        
        # Convert the Python object to a JSON string using the custom encoder for datetime objects
        ingest_endpoint = os.environ.get("INGEST_ENDPOINT", "http://52.233.88.40:3000/ingest")
        print(f"Sending DORA metrics data to ingest endpoint: {ingest_endpoint}")
        
        print(dora_metrics_data) 
   
      
    
        # Send the data to the ingest endpoint
        # response = requests.post(
        #     ingest_endpoint,
        #     # Use the custom encoder to handle datetime objects
        #     data=json.dumps(dora_metrics_data, cls=DateTimeEncoder),
        #     headers={"Content-Type": "application/json"},
        #     timeout=30
        # )
        
        # # Check response
        # response.raise_for_status()
        print(f"Successfully sent data to ingest endpoint. Status code: {response.status_code}")
        
        # Try to get JSON response
        try:
            result = response.json()
            print(f"Response: {json.dumps(result, indent=2)}")
        except:
            print(f"Response: {response.text}")
                
    except Exception as e:
        print(f"Error in send_to_ingest_callback: {str(e)}")
        
    #Return None to keep the original response unchanged
    return None

async def create_sequential_agent():
    """Creates the main sequential agent that orchestrates all sub-agents."""
    exit_stack = AsyncExitStack()
    
    # Create GitHub agent
    github_agent, github_exit_stack = await create_github_agent()
    await exit_stack.enter_async_context(github_exit_stack)
    
    # Create Jenkins agent
    jenkins_agent, jenkins_exit_stack = await create_jenkins_agent()
    await exit_stack.enter_async_context(jenkins_exit_stack)
    
    # Create JFrog agent
    jfrog_agent, jfrog_exit_stack = await create_jfrog_agent()
    await exit_stack.enter_async_context(jfrog_exit_stack)
    
    # Create mapping agent (doesn't need tools)
    mapping_agent, _ = await create_mapping_agent()
    
    # Create sequential agent
    sequential_agent = SequentialAgent(
        name="DORAMetricsPipelineAgent",
        sub_agents=[github_agent, jenkins_agent, jfrog_agent, mapping_agent],
        description="Analyzes PR and build data to calculate DORA metrics"
    )
    
    return sequential_agent, exit_stack

# For ADK tools compatibility, the root agent must be named `root_agent`
root_agent = create_sequential_agent()
