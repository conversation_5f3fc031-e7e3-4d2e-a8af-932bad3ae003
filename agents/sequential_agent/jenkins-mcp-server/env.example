# Jenkins MCP Server Configuration
# Copy this file to .env and fill in your <PERSON> instance details

# Primary Jenkins Instance (Production)
JENKINS_URL=https://prod-jenkins.yourcompany.com
JENKINS_USERNAME=your-username
JENKINS_PASSWORD=your-api-token

# Secondary Jenkins Instance (Staging)
JENKINS_2_URL=https://staging-jenkins.yourcompany.com
JENKINS_2_USERNAME=staging-username
JENKINS_2_PASSWORD=staging-api-token
JENKINS_2_NAME=staging

# Third Jenkins Instance (Development)
JENKINS_3_URL=https://dev-jenkins.yourcompany.com
JENKINS_3_USERNAME=dev-username
JENKINS_3_PASSWORD=dev-api-token
JENKINS_3_NAME=development

# Additional instances can be added with incrementing numbers:
# JENKINS_4_URL=https://test-jenkins.yourcompany.com
# JENKINS_4_USERNAME=test-username
# JENKINS_4_PASSWORD=test-api-token
# JENKINS_4_NAME=testing

# Notes:
# - JENKINS_N_NAME is optional and defaults to "jenkins_N" if not provided
# - Use API tokens instead of passwords for better security
# - The primary instance (without number) can coexist with numbered instances
# - Each instance must have URL, USERNAME, and PASSWORD/token configured 