# Jenkins MCP Server - Multi-Instance Support

This MCP (Model Context Protocol) server provides tools for interacting with multiple Jenkins instances simultaneously. It supports searching across all instances or targeting specific instances for operations.

## Features

- **Multi-Instance Support**: Connect to multiple Jenkins servers simultaneously
- **Cross-Instance Search**: Search for jobs, builds, and commit hashes across all instances
- **Instance-Specific Operations**: Target specific Jenkins instances for operations
- **Parallel Processing**: Optimized for concurrent operations across instances
- **Error Resilience**: Continues working even if some instances are unavailable

## Configuration

### Environment Variables

The server supports multiple Jenkins instances through numbered environment variables:

#### Single Instance (Backward Compatible)
```bash
JENKINS_URL=https://your-jenkins.example.com
JENKINS_USERNAME=your-username
JENKINS_PASSWORD=your-password-or-token
```

#### Multiple Instances
```bash
# Instance 1 (uses default naming for backward compatibility)
JENKINS_URL=https://jenkins1.example.com
JENKINS_USERNAME=username1
JENKINS_PASSWORD=password1

# Instance 2
JENKINS_2_URL=https://jenkins2.example.com
JENKINS_2_USERNAME=username2
JENKINS_2_PASSWORD=password2
JENKINS_2_NAME=staging-jenkins

# Instance 3
JENKINS_3_URL=https://jenkins3.example.com
JENKINS_3_USERNAME=username3
JENKINS_3_PASSWORD=password3
JENKINS_3_NAME=production-jenkins
```

#### Environment Variable Format
- `JENKINS_URL` or `JENKINS_N_URL`: Jenkins server URL
- `JENKINS_USERNAME` or `JENKINS_N_USERNAME`: Username for authentication
- `JENKINS_PASSWORD` or `JENKINS_N_PASSWORD`: Password or API token
- `JENKINS_N_NAME` (optional): Custom name for the instance (defaults to `jenkins_N`)

Where `N` is the instance number (2, 3, 4, etc.).

### .env File Example
```env
# Production Jenkins
JENKINS_URL=https://prod-jenkins.company.com
JENKINS_USERNAME=prod-user
JENKINS_PASSWORD=prod-api-token

# Staging Jenkins
JENKINS_2_URL=https://staging-jenkins.company.com
JENKINS_2_USERNAME=staging-user
JENKINS_2_PASSWORD=staging-api-token
JENKINS_2_NAME=staging

# Development Jenkins
JENKINS_3_URL=https://dev-jenkins.company.com
JENKINS_3_USERNAME=dev-user
JENKINS_3_PASSWORD=dev-api-token
JENKINS_3_NAME=development
```

## Available Tools

### 1. `list_instances`
Lists all configured Jenkins instances with their basic information.

**Parameters:** None

**Returns:** Information about all connected Jenkins instances including URLs, usernames, and versions.

### 2. `list_jobs`
Lists Jenkins jobs from one or all instances.

**Parameters:**
- `instance_name` (optional): Name of the Jenkins instance to query. If not provided, queries all instances.

**Examples:**
- `list_jobs()` - Lists jobs from all instances
- `list_jobs(instance_name="staging")` - Lists jobs from staging instance only

### 3. `get_job_status`
Gets the current status of a Jenkins job from one or all instances.

**Parameters:**
- `job_name` (required): Name of the job
- `instance_name` (optional): Name of the Jenkins instance to query. If not provided, searches all instances.

**Examples:**
- `get_job_status(job_name="my-app-build")` - Searches for job across all instances
- `get_job_status(job_name="my-app-build", instance_name="production")` - Checks job on production instance only

### 4. `check_jenkins_connection`
Checks connection to Jenkins server(s) and gets basic information.

**Parameters:**
- `instance_name` (optional): Name of the Jenkins instance to check. If not provided, checks all instances.

**Examples:**
- `check_jenkins_connection()` - Checks all instances
- `check_jenkins_connection(instance_name="staging")` - Checks staging instance only

### 5. `get_build_by_commit_hash`
Searches for builds with a specific git commit hash across one or all Jenkins instances.

**Parameters:**
- `commit_hash` (required): Git commit hash to search for
- `job_name` (optional): Name of the job to search in. If not provided, searches all jobs.
- `instance_name` (optional): Name of the Jenkins instance to search in. If not provided, searches all instances.

**Examples:**
- `get_build_by_commit_hash(commit_hash="abc123def456")` - Searches all instances and jobs
- `get_build_by_commit_hash(commit_hash="abc123def456", job_name="my-app")` - Searches specific job across all instances
- `get_build_by_commit_hash(commit_hash="abc123def456", instance_name="production")` - Searches all jobs on production instance
- `get_build_by_commit_hash(commit_hash="abc123def456", job_name="my-app", instance_name="staging")` - Searches specific job on staging instance

## Usage Patterns

### Cross-Instance Operations
Most tools support operating across all instances by omitting the `instance_name` parameter:

```python
# Check health of all Jenkins instances
status = check_jenkins_connection()

# Find a job across all instances
job_status = get_job_status(job_name="critical-service-build")

# Search for a commit across all instances and jobs
builds = get_build_by_commit_hash(commit_hash="a1b2c3d4e5f6")
```

### Instance-Specific Operations
Target specific instances by providing the `instance_name` parameter:

```python
# Check only production Jenkins
prod_status = check_jenkins_connection(instance_name="production")

# List jobs only from staging
staging_jobs = list_jobs(instance_name="staging")

# Search for builds on development instance only
dev_builds = get_build_by_commit_hash(
    commit_hash="a1b2c3d4e5f6", 
    instance_name="development"
)
```

### Error Handling
The server is designed to be resilient to individual instance failures:

- If one instance is down, operations continue with available instances
- Error information is included in responses for failed instances
- Cross-instance searches aggregate results from all available instances

## Performance Considerations

- **Parallel Processing**: Uses ThreadPoolExecutor for concurrent operations across instances and builds
- **Optimized for Multi-Core**: Configured for 8 worker threads, suitable for modern multi-core processors
- **Timeout Configuration**: Uses 1000ms timeout for Jenkins connections
- **Efficient Searching**: When searching across instances, operations run in parallel

## Installation and Running

1. Install dependencies:
```bash
pip install fastmcp python-jenkins python-dotenv
```

2. Create your `.env` file with Jenkins configurations

3. Run the server:
```bash
python main.py
```

## Troubleshooting

### Common Issues

1. **Instance Not Found Error**: 
   - Check that environment variables are set correctly
   - Verify instance names match what's configured

2. **Connection Failures**:
   - Use `check_jenkins_connection()` to verify connectivity
   - Check URLs, usernames, and passwords/tokens
   - Ensure Jenkins instances are accessible from the server

3. **Performance Issues**:
   - Reduce the number of instances if experiencing timeouts
   - Consider using instance-specific operations for large Jenkins instances

### Debug Information
The server provides detailed debug logging to stderr. Check the logs for:
- Instance discovery and connection status
- Operation progress and errors
- Performance timing information

## Migration from Single Instance

If you're migrating from a single-instance setup, your existing environment variables will continue to work. Simply add numbered variables for additional instances:

```bash
# Existing (continues to work)
JENKINS_URL=https://main-jenkins.company.com
JENKINS_USERNAME=main-user
JENKINS_PASSWORD=main-token

# Add additional instances
JENKINS_2_URL=https://backup-jenkins.company.com
JENKINS_2_USERNAME=backup-user
JENKINS_2_PASSWORD=backup-token
JENKINS_2_NAME=backup-jenkins
``` 