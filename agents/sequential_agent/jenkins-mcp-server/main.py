from dataclasses import dataclass
from typing import AsyncIterator, List, Optional
from mcp.server.fastmcp import FastMCP, Context
import requests
from contextlib import asynccontextmanager
import os
import sys
import urllib3
from typing import AsyncIterator, List, Dict, Optional, Union, Any, Tuple
from concurrent.futures import Thread<PERSON>oolExecutor, as_completed
from datetime import datetime, timezone
import json

# Disable SSL warnings
urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)

def debug_log(message):
    print(f"DEBUG: {message}", file=sys.stderr)

debug_log("Starting Jenkins MCP server...")

class DirectJenkinsClient:
    """Direct Jenkins API client using requests"""

    def __init__(self, url, username, password):
        self.url = url.rstrip('/')
        self.username = username
        self.password = password
        self.session = requests.Session()
        self.session.auth = (username, password)
        self.session.verify = False  # Disable SSL verification
        self.session.timeout = 30

    def get_version(self):
        """Get Jenkins version"""
        response = self.session.get(f"{self.url}/api/json")
        response.raise_for_status()
        return response.headers.get('x-jenkins', 'unknown')

    def get_jobs(self):
        """Get list of jobs"""
        response = self.session.get(f"{self.url}/api/json?tree=jobs[name,url]")
        response.raise_for_status()
        data = response.json()
        return data.get('jobs', [])

    def get_job_info(self, job_name):
        """Get job information"""
        response = self.session.get(f"{self.url}/job/{job_name}/api/json")
        if response.status_code == 404:
            raise JobNotFoundException(f"Job '{job_name}' not found")
        response.raise_for_status()
        return response.json()

    def get_build_info(self, job_name, build_number):
        """Get build information"""
        response = self.session.get(f"{self.url}/job/{job_name}/{build_number}/api/json")
        response.raise_for_status()
        return response.json()

class JobNotFoundException(Exception):
    """Exception raised when a job is not found"""
    pass

@dataclass
class JenkinsContext:
    clients: Dict[str, DirectJenkinsClient]  # Map of instance_name -> DirectJenkinsClient
    instance_configs: Dict[str, Dict[str, str]]  # Map of instance_name -> config dict


@asynccontextmanager
async def jenkins_lifespan(server: FastMCP) -> AsyncIterator[JenkinsContext]:
    """Manage Jenkins client lifecycle for multiple instances"""
    debug_log("Starting Jenkins lifespan")
    try:
        # read .env
        import dotenv
        debug_log("Loading environment variables")
        dotenv.load_dotenv()

        clients = {}
        instance_configs = {}

        # Define Jenkins instances with their environment variable mappings
        jenkins_instances = [
            {
                "name": "jenkins_dev",
                "environment": "qa-dev",
                "url_key": "JENKINS_DEV_URL",
                "username_key": "JENKINS_DEV_USERNAME",
                "password_key": "JENKINS_DEV_PASSWORD"
            },
            {
                "name": "jenkins_uat",
                "environment": "qa-sit",
                "url_key": "JENKINS_UAT_URL",
                "username_key": "JENKINS_UAT_USERNAME",
                "password_key": "JENKINS_UAT_PASSWORD"
            },
            {
                "name": "jenkins_prod",
                "environment": "qa-stage,qa-prod",
                "url_key": "JENKINS_PROD_URL",
                "username_key": "JENKINS_PROD_USERNAME",
                "password_key": "JENKINS_PROD_PASSWORD"
            },
            # Legacy support for backward compatibility
            {
                "name": "jenkins_legacy",
                "environment": "legacy",
                "url_key": "JENKINS_URL",
                "username_key": "JENKINS_USERNAME",
                "password_key": "JENKINS_API_TOKEN"
            }
        ]

        # Try to connect to each Jenkins instance
        for instance_config in jenkins_instances:
            jenkins_url = os.environ.get(instance_config["url_key"])
            username = os.environ.get(instance_config["username_key"])
            password = os.environ.get(instance_config["password_key"])
            instance_name = instance_config["name"]
            environment = instance_config["environment"]

            if not jenkins_url:
                debug_log(f"Skipping {instance_name}: No URL configured ({instance_config['url_key']})")
                continue

            if not username or not password:
                debug_log(f"Skipping {instance_name}: Missing username or password")
                continue

            debug_log(f"Instance {instance_name} ({environment}): URL: {jenkins_url}")
            debug_log(f"Instance {instance_name}: Username: {'Set' if username else 'Not set'}")
            debug_log(f"Instance {instance_name}: Password: {'Set' if password else 'Not set'}")

            try:
                debug_log(f"Connecting to Jenkins instance '{instance_name}' at {jenkins_url}")

                # Create DirectJenkinsClient
                client = DirectJenkinsClient(jenkins_url, username, password)

                # Test the connection with retry logic
                max_retries = 3
                for attempt in range(max_retries):
                    try:
                        debug_log(f"Testing connection to '{instance_name}' (attempt {attempt + 1}/{max_retries})")
                        version = client.get_version()
                        debug_log(f"Connected to Jenkins instance '{instance_name}' successfully (version: {version})")

                        clients[instance_name] = client
                        instance_configs[instance_name] = {
                            "url": jenkins_url,
                            "username": username,
                            "version": version,
                            "environment": environment
                        }
                        break  # Success, exit retry loop

                    except Exception as retry_e:
                        debug_log(f"Connection attempt {attempt + 1} failed for '{instance_name}': {str(retry_e)}")
                        if attempt == max_retries - 1:  # Last attempt
                            raise retry_e
                        import time
                        time.sleep(2)  # Wait 2 seconds before retry

            except Exception as e:
                debug_log(f"Failed to connect to Jenkins instance '{instance_name}' after {max_retries} attempts: {str(e)}")
                # Continue with other instances instead of failing completely

        if not clients:
            debug_log("No valid Jenkins instances found. Available environment variables:")
            for instance_config in jenkins_instances:
                url = os.environ.get(instance_config["url_key"])
                username = os.environ.get(instance_config["username_key"])
                password = os.environ.get(instance_config["password_key"])
                debug_log(f"  {instance_config['name']}: URL={url}, Username={'Set' if username else 'Not set'}, Password={'Set' if password else 'Not set'}")
            raise Exception("No valid Jenkins instances found. Please check your environment variables.")

        debug_log(f"Successfully connected to {len(clients)} Jenkins instance(s): {list(clients.keys())}")
        yield JenkinsContext(clients=clients, instance_configs=instance_configs)

    except Exception as e:
        debug_log(f"Error in Jenkins lifespan: {str(e)}")
        # Re-raise the exception to properly handle errors
        raise
    finally:
        debug_log("Exiting Jenkins lifespan")


mcp = FastMCP("jenkins-mcp", lifespan=jenkins_lifespan)
debug_log("FastMCP initialized")
@mcp.tool()
def list_instances(ctx: Context) -> Dict[str, Any]:
    """List all configured Jenkins instances with their basic information

    Returns:
        Dictionary containing information about all configured Jenkins instances
    """
    debug_log("Listing Jenkins instances")
    jenkins_ctx = ctx.request_context.lifespan_context

    instances_info = {}
    for instance_name, config in jenkins_ctx.instance_configs.items():
        # Check if this instance has an active client connection
        is_connected = instance_name in jenkins_ctx.clients
        status = config.get("status", "connected" if is_connected else "connection_failed")

        instances_info[instance_name] = {
            "name": instance_name,
            "url": config["url"],
            "username": config["username"],
            "version": config["version"],
            "environment": config["environment"],
            "status": status,
            "connected": is_connected
        }

    return {
        "total_instances": len(instances_info),
        "connected_instances": len(jenkins_ctx.clients),
        "instances": instances_info
    }


@mcp.tool()
def list_jobs(ctx: Context, instance_name: str = None) -> Dict[str, Any]:
    """List all Jenkins jobs from one or all instances

    Args:
        instance_name: Name of the Jenkins instance to query. If not provided, queries all instances.

    Returns:
        Dictionary containing jobs from the specified instance or all instances
    """
    debug_log(f"Listing Jenkins jobs for instance: {instance_name or 'all instances'}")
    jenkins_ctx = ctx.request_context.lifespan_context

    if instance_name:
        # Query specific instance
        if instance_name not in jenkins_ctx.clients:
            return {"error": f"Instance '{instance_name}' not found. Available instances: {list(jenkins_ctx.clients.keys())}"}

        client = jenkins_ctx.clients[instance_name]
        jobs = client.get_jobs()
        return {
            "instance": instance_name,
            "jobs": jobs
        }
    else:
        # Query all instances
        all_jobs = {}
        for inst_name, client in jenkins_ctx.clients.items():
            try:
                jobs = client.get_jobs()
                all_jobs[inst_name] = {
                    "instance": inst_name,
                    "jobs": jobs,
                    "job_count": len(jobs)
                }
            except Exception as e:
                debug_log(f"Error getting jobs from instance {inst_name}: {str(e)}")
                all_jobs[inst_name] = {
                    "instance": inst_name,
                    "error": str(e)
                }

        return {
            "all_instances": all_jobs,
            "total_instances": len(all_jobs)
        }


@mcp.tool()
def get_job_status(ctx: Context, job_name: str, instance_name: str = None) -> Dict[str, Any]:
    """Get current status of a Jenkins job from one or all instances

    Args:
        job_name: Name of the job
        instance_name: Name of the Jenkins instance to query. If not provided, searches all instances.

    Returns:
        Dictionary containing job status information
    """
    debug_log(f"Getting job status for: {job_name} on instance: {instance_name or 'all instances'}")
    jenkins_ctx = ctx.request_context.lifespan_context

    if instance_name:
        # Query specific instance
        if instance_name not in jenkins_ctx.clients:
            return {"error": f"Instance '{instance_name}' not found. Available instances: {list(jenkins_ctx.clients.keys())}"}

        client = jenkins_ctx.clients[instance_name]
        try:
            job_info = client.get_job_info(job_name)
            return {
                "instance": instance_name,
                "name": job_name,
                "url": job_info["url"],
                "last_build_number": job_info.get("lastBuild", {}).get("number"),
                "last_build_status": job_info.get("lastBuild", {}).get("result"),
                "last_build_url": job_info.get("lastBuild", {}).get("url"),
                "next_build_number": job_info.get("nextBuildNumber"),
                "in_queue": job_info.get("inQueue", False),
                "concurrent_build": job_info.get("concurrentBuild", False),
                "disabled": job_info.get("disabled", False)
            }
        except JobNotFoundException:
            return {"error": f"Job '{job_name}' not found on instance '{instance_name}'"}
        except Exception as e:
            return {"error": f"Error getting job status from instance '{instance_name}': {str(e)}"}
    else:
        # Search all instances
        results = {}
        found_instances = []

        for inst_name, client in jenkins_ctx.clients.items():
            try:
                job_info = client.get_job_info(job_name)
                results[inst_name] = {
                    "instance": inst_name,
                    "name": job_name,
                    "url": job_info["url"],
                    "last_build_number": job_info.get("lastBuild", {}).get("number"),
                    "last_build_status": job_info.get("lastBuild", {}).get("result"),
                    "last_build_url": job_info.get("lastBuild", {}).get("url"),
                    "next_build_number": job_info.get("nextBuildNumber"),
                    "in_queue": job_info.get("inQueue", False),
                    "concurrent_build": job_info.get("concurrentBuild", False),
                    "disabled": job_info.get("disabled", False)
                }
                found_instances.append(inst_name)
            except JobNotFoundException:
                debug_log(f"Job '{job_name}' not found on instance '{inst_name}'")
                results[inst_name] = {"error": f"Job '{job_name}' not found"}
            except Exception as e:
                debug_log(f"Error getting job status from instance {inst_name}: {str(e)}")
                results[inst_name] = {"error": str(e)}

        return {
            "job_name": job_name,
            "found_on_instances": found_instances,
            "results": results,
            "total_instances_checked": len(results)
        }

@mcp.tool()
def check_jenkins_connection(ctx: Context, instance_name: str = None) -> Dict[str, Any]:
    """Check connection to Jenkins server(s) and get basic information.

    Args:
        instance_name: Name of the Jenkins instance to check. If not provided, checks all instances.

    Returns:
        Dictionary containing connection status, Jenkins version, and other info
    """
    debug_log(f"Checking Jenkins connection for instance: {instance_name or 'all instances'}")
    jenkins_ctx = ctx.request_context.lifespan_context

    if instance_name:
        # Check specific instance
        if instance_name not in jenkins_ctx.clients:
            return {"error": f"Instance '{instance_name}' not found. Available instances: {list(jenkins_ctx.clients.keys())}"}

        client = jenkins_ctx.clients[instance_name]
        config = jenkins_ctx.instance_configs[instance_name]

        try:
            # Get Jenkins version (this also tests connectivity)
            version = client.get_version()

            return {
                "instance": instance_name,
                "status": "connected",
                "version": version,
                "url": config["url"],
                "username": config["username"],
                "environment": config["environment"]
            }
        except Exception as e:
            debug_log(f"Error checking Jenkins connection for {instance_name}: {str(e)}")
            return {
                "instance": instance_name,
                "status": "error",
                "error": str(e)
            }
    else:
        # Check all instances
        results = {}
        for inst_name, client in jenkins_ctx.clients.items():
            config = jenkins_ctx.instance_configs[inst_name]
            try:
                version = client.get_version()
                results[inst_name] = {
                    "instance": inst_name,
                    "status": "connected",
                    "version": version,
                    "url": config["url"],
                    "username": config["username"],
                    "environment": config["environment"]
                }
            except Exception as e:
                debug_log(f"Error checking Jenkins connection for {inst_name}: {str(e)}")
                results[inst_name] = {
                    "instance": inst_name,
                    "status": "error",
                    "error": str(e)
                }

        return {
            "total_instances": len(results),
            "results": results
        }

def format_utc_timestamp(timestamp_ms: int) -> str:
    """Convert Unix timestamp (milliseconds) to formatted UTC time string.

    Args:
        timestamp_ms: Unix timestamp in milliseconds

    Returns:
        Formatted UTC time string in format: "YYYY-MM-DD HH:MM:SS UTC"
    """
    # Convert milliseconds to seconds if needed
    if timestamp_ms > 1000000000000:  # If timestamp is in milliseconds
        timestamp_s = timestamp_ms / 1000
    else:
        timestamp_s = timestamp_ms

    # Convert to UTC datetime
    utc_time = datetime.fromtimestamp(timestamp_s, tz=timezone.utc)

    # Format the datetime
    return f"{utc_time.strftime('%Y-%m-%d %H:%M:%S')} UTC"

@mcp.tool()
def get_build_by_commit_hash(ctx: Context, commit_hash: str, job_name: str = None, instance_name: str = None) -> Dict[str, Any]:
    """Get build information for a specific git commit hash across one or all Jenkins instances

    Args:
        commit_hash: Git commit hash to search for
        job_name: Optional name of the job to search in. If not provided, will search across all jobs.
        instance_name: Optional name of the Jenkins instance to search in. If not provided, searches all instances.

    Returns:
        Dictionary containing build information including job name, build number, timestamps, and status
    """
    debug_log(f"Searching for build with commit hash: {commit_hash}, job: {job_name or 'all jobs'}, instance: {instance_name or 'all instances'}")
    jenkins_ctx = ctx.request_context.lifespan_context

    # Function to check if a build contains the commit hash
    def build_has_commit(build_info: Dict[str, Any], commit_hash: str) -> bool:
        # Check in build parameters
        for action in build_info.get("actions", []):
            # Check in parameters
            for param in action.get("parameters", []):
                if param.get("name", "").lower() in ["git_commit", "commit", "revision", "sha", "hash"] and param.get("value") == commit_hash:
                    return True

            # Check in build causes and changes
            if "lastBuiltRevision" in action:
                if action.get("lastBuiltRevision", {}).get("SHA1") == commit_hash:
                    return True

                # Check in branch information
                for branch in action.get("lastBuiltRevision", {}).get("branch", []):
                    if branch.get("SHA1") == commit_hash:
                        return True

            # Check in buildsByBranchName
            for branch_info in action.get("buildsByBranchName", {}).values():
                # Check in marked
                if branch_info.get("marked", {}).get("SHA1") == commit_hash:
                    return True

                # Check in branch information in marked
                for branch in branch_info.get("marked", {}).get("branch", []):
                    if branch.get("SHA1") == commit_hash:
                        return True

                # Check in revision
                if branch_info.get("revision", {}).get("SHA1") == commit_hash:
                    return True

                # Check in branch information in revision
                for branch in branch_info.get("revision", {}).get("branch", []):
                    if branch.get("SHA1") == commit_hash:
                        return True

        return False

    result = {
        "commit_hash": commit_hash,
        "instances_searched": [],
        "total_builds_found": 0,
        "builds_by_instance": {}
    }

    # Create a thread pool for parallel processing
    max_workers = 8  # Optimized for i5-12450H (8 cores/12 threads)

    # Process a single job
    def process_job(client, job_name, instance_name):
        job_results = []
        try:
            job_info = client.get_job_info(job_name)
            builds = job_info.get("builds", [])

            # Process builds in parallel within this job
            with ThreadPoolExecutor(max_workers=max_workers) as executor:
                # Create a function to process a single build
                def process_build(build):
                    build_number = build.get("number")
                    try:
                        build_info = client.get_build_info(job_name, build_number)

                        if build_has_commit(build_info, commit_hash):
                            # Get detailed timestamps
                            timestamp = build_info.get("timestamp", 0)  # Unix timestamp in ms
                            duration = build_info.get("duration", 0)  # Duration in ms
                            end_timestamp = timestamp + duration if duration > 0 else None

                            return {
                                "instance": instance_name,
                                "job_name": job_name,
                                "build_number": build_number,
                                "status": build_info.get("result"),
                                "url": build_info.get("url"),
                                "start_timestamp": timestamp,
                                "start_time_utc": format_utc_timestamp(timestamp),
                                "duration_ms": duration,
                                "end_timestamp": end_timestamp,
                                "end_time_utc": format_utc_timestamp(end_timestamp) if end_timestamp else None,
                                "is_building": build_info.get("building", False)
                            }
                        return None
                    except Exception as e:
                        debug_log(f"Error getting build info for {instance_name}/{job_name} #{build_number}: {str(e)}")
                        return None

                # Submit all builds for processing
                future_to_build = {executor.submit(process_build, build): build for build in builds}

                # Collect results as they complete
                for future in as_completed(future_to_build):
                    build_result = future.result()
                    if build_result:
                        job_results.append(build_result)

        except Exception as e:
            debug_log(f"Error searching in job {instance_name}/{job_name}: {str(e)}")

        return job_results

    # Determine which instances to search
    instances_to_search = {}
    if instance_name:
        if instance_name not in jenkins_ctx.clients:
            result["error"] = f"Instance '{instance_name}' not found. Available instances: {list(jenkins_ctx.clients.keys())}"
            return result
        instances_to_search[instance_name] = jenkins_ctx.clients[instance_name]
    else:
        instances_to_search = jenkins_ctx.clients

    # Search across specified instances
    for inst_name, client in instances_to_search.items():
        result["instances_searched"].append(inst_name)
        instance_builds = []

        try:
            # If job name is provided, search only in that job
            if job_name:
                job_builds = process_job(client, job_name, inst_name)
                instance_builds.extend(job_builds)
            else:
                # Search across all jobs in parallel
                jobs = client.get_jobs()

                # Use thread pool to process jobs in parallel
                with ThreadPoolExecutor(max_workers=max_workers) as executor:
                    future_to_job = {executor.submit(process_job, client, job.get("name"), inst_name): job for job in jobs}

                    for future in as_completed(future_to_job):
                        job_builds = future.result()
                        instance_builds.extend(job_builds)

        except Exception as e:
            debug_log(f"Error searching instance {inst_name}: {str(e)}")
            result["builds_by_instance"][inst_name] = {"error": str(e)}
            continue

        # Store results for this instance
        result["builds_by_instance"][inst_name] = {
            "builds": instance_builds,
            "count": len(instance_builds)
        }
        result["total_builds_found"] += len(instance_builds)

    # Create a flattened list of all builds sorted by timestamp
    all_builds = []
    for inst_data in result["builds_by_instance"].values():
        if "builds" in inst_data:
            all_builds.extend(inst_data["builds"])

    all_builds.sort(key=lambda x: x.get("start_timestamp", 0), reverse=True)
    result["all_builds_sorted"] = all_builds
    result["found"] = result["total_builds_found"] > 0

    return result

# Make sure the MCP server stays running
if __name__ == "__main__":
    mcp.run()
    debug_log("Running MCP server...")