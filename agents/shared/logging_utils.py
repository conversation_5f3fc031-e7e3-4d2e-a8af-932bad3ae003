"""
Shared logging utilities for all agents in the black-build-agents project.

This module provides centralized logging configuration and utilities that follow
ADK best practices for structured logging and state management.
"""

import logging
import time
from datetime import datetime
from typing import Dict, Any, Optional
from google.adk.agents.callback_context import CallbackContext
from google.adk.models import LlmRequest, LlmResponse
from google.genai.types import Content


class AgentLogger:
    """Enhanced logger for ADK agents with structured logging capabilities"""
    
    def __init__(self, agent_name: str, log_file: Optional[str] = None):
        self.agent_name = agent_name
        self.logger = logging.getLogger(agent_name)
        
        # Configure logging if not already configured
        if not self.logger.handlers:
            self._configure_logging(log_file)
    
    def _configure_logging(self, log_file: Optional[str] = None):
        """Configure structured logging for the agent"""
        self.logger.setLevel(logging.INFO)
        
        # Create formatter
        formatter = logging.Formatter(
            '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
        )
        
        # Console handler
        console_handler = logging.StreamHandler()
        console_handler.setFormatter(formatter)
        self.logger.addHandler(console_handler)
        
        # File handler if specified
        if log_file:
            file_handler = logging.FileHandler(log_file)
            file_handler.setFormatter(formatter)
            self.logger.addHandler(file_handler)
    
    def log_event(self, level: str, message: str, **kwargs):
        """Log a structured event with additional context"""
        extra_info = " | ".join([f"{k}={v}" for k, v in kwargs.items()])
        full_message = f"{message} | {extra_info}" if extra_info else message
        
        level_upper = level.upper()
        if level_upper == "DEBUG":
            self.logger.debug(full_message)
        elif level_upper == "INFO":
            self.logger.info(full_message)
        elif level_upper == "WARNING":
            self.logger.warning(full_message)
        elif level_upper == "ERROR":
            self.logger.error(full_message)
        else:
            self.logger.info(full_message)
    
    def log_agent_start(self, callback_context: CallbackContext, user_message: Content):
        """Log agent execution start"""
        self.log_event("INFO", "Agent execution started",
                      agent_name=self.agent_name,
                      session_id=callback_context.session.id if callback_context.session else "unknown",
                      user_message_length=len(str(user_message)))
    
    def log_agent_end(self, callback_context: CallbackContext, agent_response: Content, execution_time: float):
        """Log agent execution completion"""
        self.log_event("INFO", "Agent execution completed",
                      agent_name=self.agent_name,
                      execution_time_seconds=round(execution_time, 2),
                      response_length=len(str(agent_response)))
    
    def log_llm_call_start(self, callback_context: CallbackContext, llm_request: LlmRequest):
        """Log LLM call initiation"""
        self.log_event("DEBUG", "LLM call initiated",
                      agent_name=self.agent_name,
                      model=llm_request.model,
                      content_length=len(str(llm_request.contents)))
    
    def log_llm_call_end(self, callback_context: CallbackContext, llm_response: LlmResponse):
        """Log LLM call completion"""
        self.log_event("DEBUG", "LLM call completed",
                      agent_name=self.agent_name,
                      response_length=len(str(llm_response.content)))
    
    def log_tool_start(self, callback_context: CallbackContext, tool_name: str, tool_args: Dict[str, Any]):
        """Log tool execution start"""
        self.log_event("INFO", "Tool execution started",
                      agent_name=self.agent_name,
                      tool_name=tool_name,
                      args_count=len(tool_args))
    
    def log_tool_end(self, callback_context: CallbackContext, tool_name: str, tool_result: Dict[str, Any], success: bool):
        """Log tool execution completion"""
        self.log_event("INFO", "Tool execution completed",
                      agent_name=self.agent_name,
                      tool_name=tool_name,
                      success=success,
                      result_size=len(str(tool_result)))
        
        if not success:
            self.log_event("ERROR", "Tool execution failed",
                          tool_name=tool_name,
                          error=tool_result.get("error", "Unknown error"))
    
    def log_connection_attempt(self, service_name: str):
        """Log service connection attempt"""
        self.log_event("INFO", f"Attempting to connect to {service_name}")
    
    def log_connection_success(self, service_name: str, tools_discovered: int = 0):
        """Log successful service connection"""
        self.log_event("INFO", f"Successfully connected to {service_name}",
                      tools_discovered=tools_discovered)
    
    def log_connection_failure(self, service_name: str, error: str):
        """Log service connection failure"""
        self.log_event("ERROR", f"Failed to connect to {service_name}",
                      error=error)
    
    def log_state_update(self, state_key: str, state_value: Any):
        """Log state updates"""
        self.log_event("DEBUG", "State updated",
                      state_key=state_key,
                      state_value=str(state_value)[:100])  # Truncate long values


class BaseAgentState:
    """Base class for agent state management"""
    
    def __init__(self):
        self.created_at = datetime.now()
        self.last_updated = datetime.now()
        self.errors_count = 0
        self.execution_count = 0
    
    def update_timestamp(self):
        """Update the last_updated timestamp"""
        self.last_updated = datetime.now()
    
    def increment_errors(self):
        """Increment error count and update timestamp"""
        self.errors_count += 1
        self.update_timestamp()
    
    def increment_executions(self):
        """Increment execution count and update timestamp"""
        self.execution_count += 1
        self.update_timestamp()
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert state to dictionary for session storage"""
        return {
            "created_at": self.created_at.isoformat(),
            "last_updated": self.last_updated.isoformat(),
            "errors_count": self.errors_count,
            "execution_count": self.execution_count
        }
    
    def from_dict(self, data: Dict[str, Any]):
        """Load state from dictionary"""
        if data.get("created_at"):
            self.created_at = datetime.fromisoformat(data["created_at"])
        if data.get("last_updated"):
            self.last_updated = datetime.fromisoformat(data["last_updated"])
        self.errors_count = data.get("errors_count", 0)
        self.execution_count = data.get("execution_count", 0)


def create_standard_callbacks(agent_logger: AgentLogger, agent_state: BaseAgentState):
    """
    Create standard ADK callbacks for an agent with logging and state management.
    
    Returns a dictionary of callback functions that can be passed to agent constructors.
    """
    
    def before_agent_callback(callback_context: CallbackContext, user_message: Content) -> Optional[Content]:
        """Standard before-agent callback with logging and state management"""
        start_time = time.time()
        
        agent_logger.log_agent_start(callback_context, user_message)
        agent_state.increment_executions()
        
        # Update session state
        if callback_context.session:
            callback_context.session.state[f"{agent_logger.agent_name}:execution_start"] = start_time
            callback_context.session.state[f"{agent_logger.agent_name}:state"] = agent_state.to_dict()
        
        return None  # Allow normal execution
    
    def after_agent_callback(callback_context: CallbackContext, agent_response: Content) -> Optional[Content]:
        """Standard after-agent callback with logging and state management"""
        end_time = time.time()
        
        # Calculate execution time
        start_time = callback_context.session.state.get(f"{agent_logger.agent_name}:execution_start", end_time) if callback_context.session else end_time
        execution_time = end_time - start_time
        
        agent_logger.log_agent_end(callback_context, agent_response, execution_time)
        
        # Update session state with completion metrics
        if callback_context.session:
            callback_context.session.state[f"{agent_logger.agent_name}:execution_time"] = execution_time
            callback_context.session.state[f"{agent_logger.agent_name}:last_completion"] = end_time
            callback_context.session.state[f"{agent_logger.agent_name}:state"] = agent_state.to_dict()
        
        return None  # Use original response
    
    def before_model_callback(callback_context: CallbackContext, llm_request: LlmRequest) -> Optional[LlmResponse]:
        """Standard before-model callback with logging"""
        agent_logger.log_llm_call_start(callback_context, llm_request)
        
        # Update session state
        if callback_context.session:
            callback_context.session.state[f"{agent_logger.agent_name}:llm_calls"] = callback_context.session.state.get(f"{agent_logger.agent_name}:llm_calls", 0) + 1
        
        return None  # Allow normal LLM call
    
    def after_model_callback(callback_context: CallbackContext, llm_response: LlmResponse) -> Optional[LlmResponse]:
        """Standard after-model callback with logging"""
        agent_logger.log_llm_call_end(callback_context, llm_response)
        return None  # Use original response
    
    def before_tool_callback(callback_context: CallbackContext, tool_name: str, tool_args: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """Standard before-tool callback with logging"""
        agent_logger.log_tool_start(callback_context, tool_name, tool_args)
        return None  # Allow normal tool execution
    
    def after_tool_callback(callback_context: CallbackContext, tool_name: str, tool_result: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """Standard after-tool callback with logging and error tracking"""
        success = not tool_result.get("error", False)
        
        agent_logger.log_tool_end(callback_context, tool_name, tool_result, success)
        
        if not success:
            agent_state.increment_errors()
        
        # Update session state
        if callback_context.session:
            callback_context.session.state[f"{agent_logger.agent_name}:tool_executions"] = callback_context.session.state.get(f"{agent_logger.agent_name}:tool_executions", 0) + 1
            callback_context.session.state[f"{agent_logger.agent_name}:errors_count"] = agent_state.errors_count
        
        return None  # Use original result
    
    return {
        "before_agent_callback": before_agent_callback,
        "after_agent_callback": after_agent_callback,
        "before_model_callback": before_model_callback,
        "after_model_callback": after_model_callback,
        "before_tool_callback": before_tool_callback,
        "after_tool_callback": after_tool_callback
    }
