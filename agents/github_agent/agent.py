import asyncio
import os
from google.adk.agents import Agent
from google.adk.tools.mcp_tool.mcp_toolset import MCPToolset, StdioServerParameters
from dotenv import load_dotenv

# Load environment variables from the root .env file
load_dotenv(dotenv_path=os.path.join(os.path.dirname(__file__), '..', '..', '.env'))

github_personal_access_token = os.getenv("GITHUB_PERSONAL_ACCESS_TOKEN")

async def get_tools_async():
    """Connects to the mcp-github server via uvx and returns the tools and exit stack."""
    print("--- Attempting to start and connect to mcp-github MCP server via uvx ---")
    try:
        # Check if npx is available (basic check)
        # A more robust check might involve checking the actual command's success
        #await asyncio.create_subprocess_shell('npx --version', stdout=asyncio.subprocess.PIPE, stderr=asyncio.subprocess.PIPE)

        tools, exit_stack = await MCPToolset.from_server(
            connection_params=StdioServerParameters(
                command='python',
                args=['/home/<USER>/truxt/black-build-agents/agents/github_agent/github-mcp-server/server.py'],
                # Optional: Add environment variables if needed by the MCP server,
                # e.g., credentials if mcp-reddit required them.
                # env=os.environ.copy()
                # Pass the API key as an environment variable to the npx process
                env={
                    "GITHUB_PERSONAL_ACCESS_TOKEN": github_personal_access_token
                }
            )
        )

        print(f"--- Successfully connected to github server. Discovered {len(tools)} tool(s). ---")
        # Print discovered tool names for debugging/instruction refinement
        for tool in tools:
            print(f"  - Discovered tool: {tool.name}") # Tool name is likely 'fetch_reddit_hot_threads' or similar
        return tools, exit_stack
    except Exception as e:
        print(f"--- ERROR connecting to or starting github server: {e} ---")
        # Return empty tools and a no-op exit stack
        class DummyExitStack:
            async def __aenter__(self): return self
            async def __aexit__(self, *args): pass
        return [], DummyExitStack()


async def create_agent():
    """Creates the agent instance after fetching tools from the MCP server."""
    tools, exit_stack = await get_tools_async()
    discovered_tool_name = "fetch_github_repo_issues"
    if not tools:
        print("--- WARNING: No tools discovered from MCP server. Agent will lack Github functionality. ---")
    
    agent_instance = Agent(
        name="async_github_agent",
        description="A Github agent has access to github repository using an external MCP Github tool.",
        model="gemini-2.5-flash-preview-04-17", # Ensure API key is in .env
        instruction=(
            "You are the Async GitHub Repository Scout. Your task is to track changes and calculate lead time across environments (dev->UAT->stage->prod) using the connected GitHub MCP tools."
            "1. **Identify Change:** Determine which change to track by PR number."
            "2. **Track Change Flow Continuously:**"
            "   - Start with 'find_pr_commits' to get all initial commits in the PR"
            "   - CONTINUOUSLY track each commit until it reaches the main branch:"
            "     a. Use 'find_commit_branches' repeatedly to check which branches contain each commit"
            "     b. For each commit, track its progression through environments (develop → UAT → staging → main)"
            "     c. Continue tracking until ALL commits are confirmed to be in the main branch"
            "     d. If a commit isn't found in main, explain possible reasons (still in progress, rejected, etc.)"
            "   - Once commits reach main, use 'get_commit_details' to gather detailed information about ALL commits"
            "     found along the way (including intermediate branch merges)"
            "3. **Calculate Lead Time:**"
            "   - Track timestamps at each environment transition"
            "   - Account for different merge types (squash/merge/rebase)"
            "   - Calculate time spent in each environment"
            "   - Record total time from initial commit to main branch"
            "4. **Present Results:**"
            "   - Show the complete progression path of each commit across environments"
            "   - Display precise timestamps at each transition point"
            "   - Calculate and show total lead time"
            "   - Identify any bottlenecks or delays in specific environments"
            "5. **Handle Edge Cases:**"
            "   - If commits take different paths through environments, document each path"
            "   - If merge types change during progression, explain the implications"
            "   - If commits are lost or replaced during merges, explain what happened"
            "6. **Do Not Hallucinate:** Only provide information returned by the tools."
        ),
        tools=tools,
    )
    
    return agent_instance, exit_stack

root_agent = create_agent()


