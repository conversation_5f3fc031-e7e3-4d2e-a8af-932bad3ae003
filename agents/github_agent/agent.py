import asyncio
import os
import logging
import time
from datetime import datetime
from typing import Optional, Dict, Any
from google.adk.agents import Agent
from google.adk.tools.mcp_tool.mcp_toolset import MCPToolset, StdioServerParameters
from google.adk.agents.callback_context import CallbackContext
from google.adk.models import LlmRequest, LlmResponse
from google.genai.types import Content, Part
from dotenv import load_dotenv

# Load environment variables from the root .env file
load_dotenv(dotenv_path=os.path.join(os.path.dirname(__file__), '..', '..', '.env'))

github_personal_access_token = os.getenv("GITHUB_PERSONAL_ACCESS_TOKEN")

# Configure structured logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler('github_agent.log')
    ]
)
logger = logging.getLogger('github_agent')

class GitHubAgentState:
    """Manages state for the GitHub agent"""

    def __init__(self):
        self.connection_status = "disconnected"
        self.tools_discovered = 0
        self.api_calls_made = 0
        self.rate_limit_remaining = None
        self.rate_limit_reset_time = None
        self.last_api_call_time = None
        self.errors_count = 0
        self.repositories_analyzed = set()
        self.commits_tracked = {}
        self.pr_analysis_progress = {}

    def to_dict(self) -> Dict[str, Any]:
        """Convert state to dictionary for session storage"""
        return {
            "connection_status": self.connection_status,
            "tools_discovered": self.tools_discovered,
            "api_calls_made": self.api_calls_made,
            "rate_limit_remaining": self.rate_limit_remaining,
            "rate_limit_reset_time": self.rate_limit_reset_time.isoformat() if self.rate_limit_reset_time else None,
            "last_api_call_time": self.last_api_call_time.isoformat() if self.last_api_call_time else None,
            "errors_count": self.errors_count,
            "repositories_analyzed": list(self.repositories_analyzed),
            "commits_tracked": self.commits_tracked,
            "pr_analysis_progress": self.pr_analysis_progress
        }

    def from_dict(self, data: Dict[str, Any]):
        """Load state from dictionary"""
        self.connection_status = data.get("connection_status", "disconnected")
        self.tools_discovered = data.get("tools_discovered", 0)
        self.api_calls_made = data.get("api_calls_made", 0)
        self.rate_limit_remaining = data.get("rate_limit_remaining")
        if data.get("rate_limit_reset_time"):
            self.rate_limit_reset_time = datetime.fromisoformat(data["rate_limit_reset_time"])
        if data.get("last_api_call_time"):
            self.last_api_call_time = datetime.fromisoformat(data["last_api_call_time"])
        self.errors_count = data.get("errors_count", 0)
        self.repositories_analyzed = set(data.get("repositories_analyzed", []))
        self.commits_tracked = data.get("commits_tracked", {})
        self.pr_analysis_progress = data.get("pr_analysis_progress", {})

# Global state instance
github_agent_state = GitHubAgentState()

def log_github_event(level: str, message: str, **kwargs):
    """Structured logging for GitHub agent events"""
    extra_info = " | ".join([f"{k}={v}" for k, v in kwargs.items()])
    full_message = f"{message} | {extra_info}" if extra_info else message

    if level.upper() == "DEBUG":
        logger.debug(full_message)
    elif level.upper() == "INFO":
        logger.info(full_message)
    elif level.upper() == "WARNING":
        logger.warning(full_message)
    elif level.upper() == "ERROR":
        logger.error(full_message)

def before_github_agent_callback(callback_context: CallbackContext, user_message: Content) -> Optional[Content]:
    """Callback executed before GitHub agent starts processing"""
    start_time = time.time()

    log_github_event("INFO", "GitHub agent execution started",
                    agent_name=callback_context.agent_name,
                    session_id=callback_context.session.id if callback_context.session else "unknown",
                    user_message_length=len(str(user_message)))

    # Update session state
    if callback_context.session:
        callback_context.session.state["github_agent:execution_start"] = start_time
        callback_context.session.state["github_agent:state"] = github_agent_state.to_dict()

    return None  # Allow normal execution

def after_github_agent_callback(callback_context: CallbackContext, agent_response: Content) -> Optional[Content]:
    """Callback executed after GitHub agent completes processing"""
    end_time = time.time()

    # Calculate execution time
    start_time = callback_context.session.state.get("github_agent:execution_start", end_time) if callback_context.session else end_time
    execution_time = end_time - start_time

    log_github_event("INFO", "GitHub agent execution completed",
                    agent_name=callback_context.agent_name,
                    execution_time_seconds=round(execution_time, 2),
                    response_length=len(str(agent_response)),
                    api_calls_made=github_agent_state.api_calls_made)

    # Update session state with completion metrics
    if callback_context.session:
        callback_context.session.state["github_agent:execution_time"] = execution_time
        callback_context.session.state["github_agent:last_completion"] = end_time
        callback_context.session.state["github_agent:state"] = github_agent_state.to_dict()

    return None  # Use original response

def before_github_model_callback(callback_context: CallbackContext, llm_request: LlmRequest) -> Optional[LlmResponse]:
    """Callback executed before LLM model call"""
    log_github_event("DEBUG", "LLM call initiated",
                     agent_name=callback_context.agent_name,
                     model=llm_request.model,
                     content_length=len(str(llm_request.contents)))

    # Update session state
    if callback_context.session:
        callback_context.session.state["github_agent:llm_calls"] = callback_context.session.state.get("github_agent:llm_calls", 0) + 1

    return None  # Allow normal LLM call

def after_github_model_callback(callback_context: CallbackContext, llm_response: LlmResponse) -> Optional[LlmResponse]:
    """Callback executed after LLM model call"""
    log_github_event("DEBUG", "LLM call completed",
                     agent_name=callback_context.agent_name,
                     response_length=len(str(llm_response.content)))

    return None  # Use original response

def before_github_tool_callback(callback_context: CallbackContext, tool_name: str, tool_args: Dict[str, Any]) -> Optional[Dict[str, Any]]:
    """Callback executed before tool execution"""
    log_github_event("INFO", "GitHub tool execution started",
                     agent_name=callback_context.agent_name,
                     tool_name=tool_name,
                     args_count=len(tool_args))

    # Track GitHub API calls specifically
    if any(keyword in tool_name.lower() for keyword in ["github", "repo", "commit", "pr", "branch"]):
        github_agent_state.api_calls_made += 1
        github_agent_state.last_api_call_time = datetime.now()

        # Track repository analysis
        if "repo" in tool_args or "repository" in tool_args:
            repo_name = tool_args.get("repo") or tool_args.get("repository")
            if repo_name:
                github_agent_state.repositories_analyzed.add(repo_name)

        # Track PR analysis progress
        if "pr" in tool_args or "pull_request" in tool_args:
            pr_number = tool_args.get("pr") or tool_args.get("pull_request")
            if pr_number:
                github_agent_state.pr_analysis_progress[str(pr_number)] = "in_progress"

        # Update session state
        if callback_context.session:
            callback_context.session.state["github_agent:api_calls_made"] = github_agent_state.api_calls_made
            callback_context.session.state["github_agent:last_api_call_time"] = github_agent_state.last_api_call_time.isoformat()
            callback_context.session.state["github_agent:repositories_analyzed"] = list(github_agent_state.repositories_analyzed)

    return None  # Allow normal tool execution

def after_github_tool_callback(callback_context: CallbackContext, tool_name: str, tool_result: Dict[str, Any]) -> Optional[Dict[str, Any]]:
    """Callback executed after tool execution"""
    success = not tool_result.get("error", False)

    log_github_event("INFO", "GitHub tool execution completed",
                     agent_name=callback_context.agent_name,
                     tool_name=tool_name,
                     success=success,
                     result_size=len(str(tool_result)))

    if not success:
        github_agent_state.errors_count += 1
        log_github_event("ERROR", "GitHub tool execution failed",
                        tool_name=tool_name,
                        error=tool_result.get("error", "Unknown error"))
    else:
        # Track successful PR analysis completion
        if "pr" in tool_name.lower() and "pr" in str(tool_result):
            # Try to extract PR number from result
            for key, value in github_agent_state.pr_analysis_progress.items():
                if value == "in_progress":
                    github_agent_state.pr_analysis_progress[key] = "completed"
                    break

    # Update session state
    if callback_context.session:
        callback_context.session.state["github_agent:tool_executions"] = callback_context.session.state.get("github_agent:tool_executions", 0) + 1
        callback_context.session.state["github_agent:errors_count"] = github_agent_state.errors_count
        callback_context.session.state["github_agent:pr_analysis_progress"] = github_agent_state.pr_analysis_progress

    return None  # Use original result

async def get_tools_async():
    """Connects to the GitHub MCP server and returns the tools and exit stack."""
    log_github_event("INFO", "Attempting to connect to GitHub MCP server")

    try:
        tools, exit_stack = await MCPToolset.from_server(
            connection_params=StdioServerParameters(
                command='python',
                args=['/home/<USER>/truxt/black-build-agents/agents/github_agent/github-mcp-server/server.py'],
                env={
                    "GITHUB_PERSONAL_ACCESS_TOKEN": github_personal_access_token
                }
            )
        )

        # Update agent state
        github_agent_state.connection_status = "connected"
        github_agent_state.tools_discovered = len(tools)

        log_github_event("INFO", "Successfully connected to GitHub server",
                        tools_discovered=len(tools))

        # Log discovered tool names
        for tool in tools:
            log_github_event("DEBUG", "Discovered GitHub tool", tool_name=tool.name)

        return tools, exit_stack

    except Exception as e:
        github_agent_state.connection_status = "failed_connection_error"
        github_agent_state.errors_count += 1

        log_github_event("ERROR", "Failed to connect to GitHub server", error=str(e))

        # Return empty tools and a no-op exit stack
        class DummyExitStack:
            async def __aenter__(self): return self
            async def __aexit__(self, *args): pass
        return [], DummyExitStack()


async def create_agent():
    """Creates the agent instance after fetching tools from the MCP server."""
    log_github_event("INFO", "Creating GitHub agent instance")

    tools, exit_stack = await get_tools_async()

    if not tools:
        log_github_event("WARNING", "No tools discovered from MCP server - agent will lack GitHub functionality")
    else:
        log_github_event("INFO", "GitHub agent created successfully", tools_count=len(tools))

    agent_instance = Agent(
        name="async_github_agent",
        description="A GitHub agent with enhanced logging and state management that has access to github repository using an external MCP Github tool.",
        model="gemini-2.5-flash-preview-04-17", # Ensure API key is in .env

        # Add callbacks for enhanced observability
        before_agent_callback=before_github_agent_callback,
        after_agent_callback=after_github_agent_callback,
        before_model_callback=before_github_model_callback,
        after_model_callback=after_github_model_callback,
        before_tool_callback=before_github_tool_callback,
        after_tool_callback=after_github_tool_callback,

        instruction=(
            """You are the GitHub Repository Analysis Agent specialized in DORA Metrics tracking and lead time calculation. Your mission is to analyze GitHub repositories to support comprehensive DORA metrics understanding and provide insights into software delivery performance.

## DORA Metrics Overview:
DORA (DevOps Research and Assessment) metrics are the four key metrics that indicate the performance of a software development team:

1. **Lead Time for Changes**: Time from code committed to code successfully running in production
2. **Deployment Frequency**: How often an organization successfully releases to production
3. **Mean Time to Recovery (MTTR)**: How long it takes to recover from a failure in production
4. **Change Failure Rate**: Percentage of deployments causing a failure in production

## Environment Flow Context:
Your system tracks changes through this specific environment progression:
- **qa-dev** (Development) → **qa-sit** (System Integration Testing) → **qa-stage** (Staging) → **qa-prod** (Production)

## Your Core Capabilities:

### 1. **DORA Metrics GitHub Analysis:**
You can analyze GitHub repositories to support DORA metrics calculation and provide insights into:

**A. Lead Time for Changes Tracking:**
   - Track commits from initial development to production deployment
   - Analyze PR creation, review, and merge times
   - Calculate time spent in different development stages
   - Identify bottlenecks in the code review and merge process
   - Track commit progression through environment branches

**B. Deployment Frequency Analysis:**
   - Monitor merge frequency to main/production branches
   - Track release patterns and deployment schedules
   - Identify deployment frequency trends over time
   - Analyze batch vs. continuous deployment patterns

**C. Change Failure Rate Support:**
   - Track reverted commits and rollback PRs
   - Identify commits that caused production issues
   - Analyze failure patterns in code changes
   - Monitor hotfix and emergency deployment patterns

**D. Recovery Time Analysis:**
   - Track time from issue identification to fix deployment
   - Analyze hotfix development and deployment times
   - Monitor incident response and resolution patterns
   - Identify recovery process improvements

### 2. **GitHub Operations and Analysis:**
You can perform comprehensive GitHub analysis including:

   1. **Repository analysis** with enhanced logging and monitoring
   2. **PR and commit tracking** across environment branches
   3. **Lead time calculation** from commit to production
   4. **Branch analysis** and environment progression tracking
   5. **Code review metrics** and approval process analysis
   6. **Release pattern analysis** and deployment frequency calculation
   7. **Failure tracking** through reverts and hotfixes
   8. **Developer productivity** and collaboration metrics
   9. **Repository health** and code quality indicators
   10. **Integration analysis** with CI/CD pipelines

### 3. **Performance Analysis and Insights:**
When analyzing repositories, focus on:

**A. Lead Time Optimization:**
   - Identify longest stages in the development process
   - Highlight PRs or commits causing delays
   - Suggest process improvements for faster delivery
   - Recommend automation opportunities in code review

**B. Deployment Frequency Improvement:**
   - Analyze merge patterns and identify optimization opportunities
   - Suggest strategies for increasing deployment frequency safely
   - Identify successful development patterns to replicate

**C. Quality and Failure Reduction:**
   - Identify patterns in failed deployments or reverted commits
   - Recommend strategies for reducing change failure rates
   - Suggest improvements to code review and testing processes

**D. Developer Experience Enhancement:**
   - Analyze code review times and suggest improvements
   - Identify collaboration bottlenecks
   - Recommend workflow optimizations

### 4. **Best Practices and Guidelines:**
When analyzing repositories, always:
   - Verify data completeness before analysis
   - Provide clear explanations of your findings
   - Use appropriate metrics and calculations
   - Handle edge cases and anomalies appropriately
   - Be thorough in your analysis
   - Provide actionable insights with specific improvement suggestions
   - Explain your methodology and assumptions
   - Consider team size and project complexity in recommendations

### 5. **DORA Metrics Benchmarking:**
You understand that elite performers have:
   - **Lead Time**: Less than one hour from commit to production
   - **Deployment Frequency**: Multiple deployments per day
   - **MTTR**: Less than one hour to recover from failures
   - **Change Failure Rate**: 0-15% of deployments cause failures

Use this knowledge to provide context and benchmarking for your analysis and recommendations.

Your task is to track changes and calculate lead time across environments (qa-dev → qa-sit → qa-stage → qa-prod) using the connected GitHub MCP tools, providing comprehensive DORA metrics insights and actionable recommendations for improving software delivery performance."""
            "1. **Identify Change:** Determine which change to track by PR number."
            "2. **Track Change Flow Continuously:**"
            "   - Start with 'find_pr_commits' to get all initial commits in the PR"
            "   - CONTINUOUSLY track each commit until it reaches the main branch:"
            "     a. Use 'find_commit_branches' repeatedly to check which branches contain each commit"
            "     b. For each commit, track its progression through environments (develop → UAT → staging → main)"
            "     c. Continue tracking until ALL commits are confirmed to be in the main branch"
            "     d. If a commit isn't found in main, explain possible reasons (still in progress, rejected, etc.)"
            "   - Once commits reach main, use 'get_commit_details' to gather detailed information about ALL commits"
            "     found along the way (including intermediate branch merges)"
            "3. **Calculate Lead Time:**"
            "   - Track timestamps at each environment transition"
            "   - Account for different merge types (squash/merge/rebase)"
            "   - Calculate time spent in each environment"
            "   - Record total time from initial commit to main branch"
            "4. **Present Results:**"
            "   - Show the complete progression path of each commit across environments"
            "   - Display precise timestamps at each transition point"
            "   - Calculate and show total lead time"
            "   - Identify any bottlenecks or delays in specific environments"
            "5. **Handle Edge Cases:**"
            "   - If commits take different paths through environments, document each path"
            "   - If merge types change during progression, explain the implications"
            "   - If commits are lost or replaced during merges, explain what happened"
            "6. **Do Not Hallucinate:** Only provide information returned by the tools."
        ),
        tools=tools,
    )

    return agent_instance, exit_stack

root_agent = create_agent()


