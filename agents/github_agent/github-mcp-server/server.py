import asyncio
import json
import os
import sys
import traceback
import re
from typing import Dict, List, Optional, Any, Union, AsyncIterator
from datetime import datetime, timedelta
from contextlib import asynccontextmanager
import requests
from mcp.server.fastmcp import FastMCP, Context
from dotenv import load_dotenv

load_dotenv()

# GitHub API configuration
GITHUB_API_BASE = "https://api.github.com"
GITHUB_API_VERSION = "2022-11-28"  # Using a stable API version

def debug_log(message, level="INFO"):
    """Write log messages to stderr with severity level
    
    Args:
        message: The message to log
        level: Severity level (INFO, WARNING, ERROR)
    """
    level = level.upper()
    print(f"GITHUB-MCP [{level}]: {message}", file=sys.stderr)

def log_exception(e, context=""):
    """Log exception with traceback
    
    Args:
        e: The exception
        context: Additional context about where the exception occurred
    """
    error_msg = f"{context}: {str(e)}" if context else str(e)
    debug_log(error_msg, "ERROR")
    debug_log(traceback.format_exc(), "ERROR")

class GitHubAPI:
    def __init__(self, token: str):
        self.token = token
        self.session = requests.Session()
        self.session.headers.update({
            "Authorization": f"token {token}",
            "Accept": "application/vnd.github.v3+json",
            "X-GitHub-Api-Version": GITHUB_API_VERSION
        })
    
    def _make_request(self, method: str, endpoint: str, **kwargs) -> Dict:
        """Make a request to the GitHub API with proper error handling
        
        Args:
            method: HTTP method (GET, POST, etc.)
            endpoint: API endpoint (without base URL)
            **kwargs: Additional arguments to pass to requests
            
        Returns:
            Dict containing the JSON response
            
        Raises:
            requests.exceptions.RequestException: If the request fails
        """
        url = f"{GITHUB_API_BASE}{endpoint}"
        try:
            response = self.session.request(method, url, **kwargs)
            response.raise_for_status()
            return response.json()
        except requests.exceptions.RequestException as e:
            if response.status_code == 404:
                raise ValueError(f"Resource not found: {endpoint}")
            elif response.status_code == 403:
                raise ValueError("API rate limit exceeded or insufficient permissions")
            else:
                raise ValueError(f"GitHub API error: {str(e)}")
    
    def get_repo(self, repo_name: str) -> Dict:
        """Get repository information"""
        return self._make_request("GET", f"/repos/{repo_name}")
    
    def get_pull_request(self, repo_name: str, pr_number: int) -> Dict:
        """Get pull request information"""
        return self._make_request("GET", f"/repos/{repo_name}/pulls/{pr_number}")
    
    def get_pull_request_commits(self, repo_name: str, pr_number: int) -> List[Dict]:
        """Get commits for a pull request"""
        return self._make_request("GET", f"/repos/{repo_name}/pulls/{pr_number}/commits")
    
    def get_pull_requests(self, repo_name: str, state: str = "open", per_page: int = 10) -> List[Dict]:
        """List pull requests in a repository"""
        return self._make_request("GET", f"/repos/{repo_name}/pulls", params={
            "state": state,
            "per_page": per_page
        })
    
    def get_commit(self, repo_name: str, commit_sha: str) -> Dict:
        """Get commit information"""
        return self._make_request("GET", f"/repos/{repo_name}/commits/{commit_sha}")
    
    def get_pull_request_reviews(self, repo_name: str, pr_number: int) -> List[Dict]:
        """Get reviews for a pull request"""
        return self._make_request("GET", f"/repos/{repo_name}/pulls/{pr_number}/reviews")
    
    def get_branches(self, repo_name: str) -> List[Dict]:
        """Get all branches in a repository"""
        return self._make_request("GET", f"/repos/{repo_name}/branches")
    
    def get_branch(self, repo_name: str, branch_name: str) -> Dict:
        """Get information about a specific branch"""
        return self._make_request("GET", f"/repos/{repo_name}/branches/{branch_name}")

debug_log("Starting GitHub MCP server...")

@asynccontextmanager
async def app_lifespan(server: FastMCP) -> AsyncIterator[GitHubAPI]:
    """Manage GitHub API client lifecycle"""
    debug_log("Starting GitHub API client lifespan")
    github_client = None
    
    try:
        # Get GitHub token from environment variable
        github_token = os.getenv("GITHUB_PERSONAL_ACCESS_TOKEN")
        if not github_token:
            debug_log("GITHUB_PERSONAL_ACCESS_TOKEN environment variable not set", "WARNING")
            github_token = os.getenv("GITHUB_TOKEN")
            if not github_token:
                debug_log("GITHUB_TOKEN environment variable not set", "ERROR")
                raise ValueError("GitHub token not found in environment variables")
        
        debug_log("Initializing GitHub API client")
        github_client = GitHubAPI(github_token)
        debug_log("GitHub API client initialized successfully")
        
        yield github_client
    except Exception as e:
        log_exception(e, "Error in GitHub API client lifespan")
        raise
    finally:
        if github_client:
            debug_log("Closing GitHub API client")
            github_client.session.close()
        debug_log("Exiting GitHub API client lifespan")

# Initialize the MCP server
mcp = FastMCP("GitHub Pull Request Analyzer", lifespan=app_lifespan, request_timeout=300)

@mcp.tool()
async def find_pr_commits(ctx: Context, repo_name: str, pr_number: int) -> str:
    """
    Find commits for a specific pull request with their timestamps and authors,
    along with base and head branch information.
    
    Args:
        repo_name: The full name of the repository (e.g., 'owner/repo')
        pr_number: The pull request number
        
    Returns:
        A formatted JSON string with pull request details including base SHA, head SHA,
        head branch name, and a list of commit details (SHA, message, timestamp, author).
    """
    debug_log(f"Finding commits for PR #{pr_number} in {repo_name}")
    try:
        # Get GitHub client from context
        gh = ctx.request_context.lifespan_context
        
        # Get pull request and commits
        pr = gh.get_pull_request(repo_name, pr_number)
        commits = gh.get_pull_request_commits(repo_name, pr_number)
        
        # Format commit information
        formatted_commits = []
        for commit in commits:
            commit_info = {
                "sha": commit["sha"],
                "message": commit["commit"]["message"],
                "timestamp": commit["commit"]["author"]["date"],
                "author": {
                    "name": commit["commit"]["author"]["name"],
                    "email": commit["commit"]["author"]["email"]
                }
            }
            # Add GitHub username if available
            if commit.get("author"):
                commit_info["author"]["login"] = commit["author"]["login"]
                
            formatted_commits.append(commit_info)
        
        response_data = {
            "pull_request_number": pr_number,
            "repository_name": repo_name,
            "base_branch_sha": pr["base"]["sha"],
            "base_branch_name": pr["base"]["ref"],
            "head_branch_sha": pr["head"]["sha"],
            "head_branch_name": pr["head"]["ref"],
            "pr_status": pr["state"],
            "merge_type": pr.get("merge_commit_sha") and "merge" or None,
            "commits": formatted_commits
        }
        
        debug_log(f"Successfully processed commits for PR #{pr_number}")
        return json.dumps(response_data, indent=2)
    
    except ValueError as e:
        log_exception(e, f"GitHub API error for PR #{pr_number}")
        return json.dumps({"error": str(e)})
    except Exception as e:
        log_exception(e, f"Error processing commits for PR #{pr_number}")
        return json.dumps({"error": f"An error occurred: {str(e)}"})

@mcp.tool()
async def list_repo_pull_requests(ctx: Context, repo_name: str, state: str = "open", limit: int = 10) -> str:
    """
    List pull requests in a GitHub repository.
    
    Args:
        repo_name: The full name of the repository (e.g., 'owner/repo')
        state: The state of the pull requests to list ('open', 'closed', or 'all')
        limit: The maximum number of pull requests to return (default: 10)
        
    Returns:
        A formatted JSON string with pull request details
    """
    debug_log(f"Listing {state} PRs for {repo_name}, limit: {limit}")
    try:
        # Get GitHub client from context
        gh = ctx.request_context.lifespan_context
        
        # Get pull requests
        pull_requests = gh.get_pull_requests(repo_name, state=state, per_page=limit)
        debug_log(f"Found {len(pull_requests)} {state} PRs for {repo_name}")
        
        # Format pull request information
        formatted_prs = []
        for pr in pull_requests:
            pr_info = {
                "number": pr["number"],
                "title": pr["title"],
                "state": pr["state"],
                "created_at": pr["created_at"],
                "updated_at": pr["updated_at"],
                "user": pr["user"]["login"] if pr["user"] else "Unknown",
                "url": pr["html_url"]
            }
            formatted_prs.append(pr_info)
        
        debug_log(f"Successfully processed {len(formatted_prs)} PRs")
        return json.dumps(formatted_prs, indent=2)
    
    except ValueError as e:
        log_exception(e, f"GitHub API error listing PRs for {repo_name}")
        return json.dumps({"error": str(e)})
    except Exception as e:
        log_exception(e, f"Error listing PRs for {repo_name}")
        return json.dumps({"error": f"An error occurred: {str(e)}"})

@mcp.tool()
async def get_commit_details(ctx: Context, repo_name: str, commit_sha: str) -> str:
    """
    Get detailed information about a specific commit.
    
    Args:
        repo_name: The full name of the repository (e.g., 'owner/repo')
        commit_sha: The SHA of the commit
        
    Returns:
        A formatted JSON string with commit details
    """
    debug_log(f"Getting details for commit {commit_sha} in {repo_name}")
    try:
        # Get GitHub client from context
        gh = ctx.request_context.lifespan_context
        
        # Get commit
        commit = gh.get_commit(repo_name, commit_sha)
        
        # Format commit information
        commit_info = {
            "sha": commit["sha"],
            "message": commit["commit"]["message"],
            "timestamp": commit["commit"]["author"]["date"],
            "author": {
                "name": commit["commit"]["author"]["name"],
                "email": commit["commit"]["author"]["email"]
            },
            "stats": commit["stats"],
            "files": [
                {
                    "filename": file["filename"],
                    "status": file["status"],
                    "additions": file["additions"],
                    "deletions": file["deletions"],
                    "changes": file["changes"]
                }
                for file in commit["files"]
            ]
        }
        
        # Add GitHub username if available
        if commit.get("author"):
            commit_info["author"]["login"] = commit["author"]["login"]
        
        debug_log(f"Successfully retrieved details for commit {commit_sha}")
        return json.dumps(commit_info, indent=2)
    
    except ValueError as e:
        log_exception(e, f"GitHub API error for commit {commit_sha}")
        return json.dumps({"error": str(e)})
    except Exception as e:
        log_exception(e, f"Error processing details for commit {commit_sha}")
        return json.dumps({"error": f"An error occurred: {str(e)}"})

@mcp.tool()
async def get_pr_stats(ctx: Context, repo_name: str, days: int = 30) -> str:
    """
    Get statistics about pull requests in a repository within a time period.
    
    Args:
        repo_name: The full name of the repository (e.g., 'owner/repo')
        days: Number of days to analyze (default: 30)
        
    Returns:
        A formatted JSON string with PR statistics
    """
    debug_log(f"Getting PR stats for {repo_name} over past {days} days")
    try:
        # Get GitHub client from context
        gh = ctx.request_context.lifespan_context
        
        # Calculate date cutoff
        cutoff_date = datetime.now().replace(tzinfo=None) - timedelta(days=days)
        
        # Get all PRs
        all_prs = gh.get_pull_requests(repo_name, state="all", per_page=100)
        debug_log(f"Retrieved {len(all_prs)} total PRs for initial analysis")
        
        # Filter PRs by date
        recent_prs = []
        for pr in all_prs:
            updated = datetime.fromisoformat(pr["updated_at"].replace("Z", "+00:00"))
            if updated.replace(tzinfo=None) >= cutoff_date:
                recent_prs.append(pr)
            else:
                # Since we're sorted by updated, we can break once we hit older PRs
                break
        
        debug_log(f"Found {len(recent_prs)} PRs updated in the past {days} days")
        
        # Calculate stats
        open_prs = sum(1 for pr in recent_prs if pr["state"] == "open")
        closed_prs = sum(1 for pr in recent_prs if pr["state"] == "closed")
        merged_prs = sum(1 for pr in recent_prs if pr.get("merged_at"))
        
        # Calculate average time to merge for merged PRs
        merge_times = []
        for pr in recent_prs:
            if pr.get("merged_at") and pr.get("created_at"):
                created = datetime.fromisoformat(pr["created_at"].replace("Z", "+00:00"))
                merged = datetime.fromisoformat(pr["merged_at"].replace("Z", "+00:00"))
                merge_time = (merged - created).total_seconds() / 3600  # in hours
                merge_times.append(merge_time)
        
        avg_merge_time = sum(merge_times) / len(merge_times) if merge_times else 0
        
        # Get top contributors
        contributors = {}
        for pr in recent_prs:
            if pr.get("user"):
                username = pr["user"]["login"]
                contributors[username] = contributors.get(username, 0) + 1
        
        top_contributors = sorted(
            [{"username": k, "pr_count": v} for k, v in contributors.items()],
            key=lambda x: x["pr_count"],
            reverse=True
        )[:5]  # Top 5 contributors
        
        stats = {
            "repository": repo_name,
            "time_period_days": days,
            "total_prs": len(recent_prs),
            "open_prs": open_prs,
            "closed_prs": closed_prs,
            "merged_prs": merged_prs,
            "avg_merge_time_hours": round(avg_merge_time, 2),
            "top_contributors": top_contributors
        }
        
        debug_log(f"Successfully calculated PR stats for {repo_name}")
        return json.dumps(stats, indent=2)
    
    except ValueError as e:
        log_exception(e, f"GitHub API error getting PR stats for {repo_name}")
        return json.dumps({"error": str(e)})
    except Exception as e:
        log_exception(e, f"Error calculating PR stats for {repo_name}")
        return json.dumps({"error": f"An error occurred: {str(e)}"})

@mcp.tool()
async def get_pr_approvals(ctx: Context, repo_name: str, pr_number: int) -> str:
    """
    Get approval events and review information for a pull request.
    
    Args:
        repo_name: The full name of the repository (e.g., 'owner/repo')
        pr_number: The pull request number
        
    Returns:
        A formatted JSON string with approval information
    """
    debug_log(f"Getting approval info for PR #{pr_number} in {repo_name}")
    try:
        gh = ctx.request_context.lifespan_context
        
        # Get PR and reviews
        pr = gh.get_pull_request(repo_name, pr_number)
        reviews = gh.get_pull_request_reviews(repo_name, pr_number)
        
        approval_info = []
        for review in reviews:
            info = {
                "id": review["id"],
                "reviewer": review["user"]["login"] if review["user"] else None,
                "state": review["state"],
                "submitted_at": review["submitted_at"],
                "commit_id": review["commit_id"],
                "body": review["body"],
                "html_url": review["html_url"]
            }
            approval_info.append(info)
        
        # Get requested reviewers from PR data
        requested_reviewers = []
        for reviewer in pr.get("requested_reviewers", []):
            requested_reviewers.append({
                "login": reviewer["login"],
                "type": "user"
            })
        for team in pr.get("requested_teams", []):
            requested_reviewers.append({
                "login": team["slug"],
                "type": "team"
            })
        
        result = {
            "reviews": approval_info,
            "requested_reviewers": requested_reviewers
        }
        
        debug_log(f"Successfully processed approval info for PR #{pr_number}")
        return json.dumps(result, indent=2)
    
    except ValueError as e:
        log_exception(e, f"GitHub API error getting approvals for PR #{pr_number}")
        return json.dumps({"error": str(e)})
    except Exception as e:
        log_exception(e, f"Error processing approvals for PR #{pr_number}")
        return json.dumps({"error": f"An error occurred: {str(e)}"})

@mcp.tool()
async def find_commit_branches(ctx: Context, repo_name: str, commit_sha: str, branch_list: List[str]) -> str:
    """
    Find all branches that contain a specific commit.
    
    Args:
        repo_name: The full name of the repository (e.g., 'owner/repo')
        commit_sha: The SHA of the commit to find
        branch_list: list of specific branch names to check. If provided, only these branches will be checked.
        
    Returns:
        A formatted JSON string with branches containing the commit, including:
        - Branch names
        - Branch types (head or remote)
        - Branch SHAs
    """
    debug_log(f"Finding branches containing commit {commit_sha} in {repo_name}")
    if branch_list:
        debug_log(f"Will only check specific branches: {', '.join(branch_list)}")
    
    try:
        # Get GitHub client from context
        gh = ctx.request_context.lifespan_context
        debug_log("GitHub client initialized")
        
        # Initialize commit info and containing branches
        commit_info = None
        containing_branches = []
        
        # Get branches to check
        if branch_list:
            # If specific branches are provided, verify they exist
            debug_log("Verifying specified branches exist...")
            branches_to_check = []
            for branch_name in branch_list:
                try:
                    branch = gh.get_branch(repo_name, branch_name)
                    branches_to_check.append(branch)
                    debug_log(f"Found branch: {branch_name}")
                except ValueError as e:
                    debug_log(f"Branch {branch_name} not found: {str(e)}", "WARNING")
            debug_log(f"Found {len(branches_to_check)} valid branches to check")
        else:
            # Get all branches if no specific list provided
            try:
                debug_log("Fetching all branches...")
                branches_to_check = gh.get_branches(repo_name)
                debug_log(f"Found {len(branches_to_check)} branches to check for commit {commit_sha}")
            except ValueError as e:
                debug_log(f"Failed to fetch branches: {str(e)}", "ERROR")
                return json.dumps({
                    "error": "Failed to fetch branches",
                    "details": str(e)
                })
        
        # Try to find the commit in the specified branches
        for branch in branches_to_check:
            try:
                debug_log(f"Checking branch {branch['name']} for commit {commit_sha}")
                # Get commits for this branch
                commits_url = f"/repos/{repo_name}/commits"
                params = {"sha": branch["name"], "per_page": 100}
                page = 1
                found = False
                
                while True:
                    params["page"] = page
                    commits = gh._make_request("GET", commits_url, params=params)
                    if not commits:
                        break
                        
                    for commit in commits:
                        if commit["sha"] == commit_sha:
                            debug_log(f"Found commit {commit_sha} in branch {branch['name']}")
                            # Found the commit! Store its info
                            if not commit_info:
                                commit_info = {
                                    "sha": commit["sha"],
                                    "message": commit["commit"]["message"],
                                    "author": commit["commit"]["author"]["name"],
                                    "date": commit["commit"]["author"]["date"]
                                }
                            
                            # Add branch info
                            branch_info = {
                                "name": branch["name"],
                                "sha": branch["commit"]["sha"],
                                "protected": branch["protected"],
                                "type": "head"
                            }
                            containing_branches.append(branch_info)
                            found = True
                            break
                    
                    if found:
                        break
                    page += 1
                    
            except ValueError as e:
                debug_log(f"Error checking branch {branch['name']}: {str(e)}", "WARNING")
                continue
            except Exception as e:
                debug_log(f"Unexpected error checking branch {branch['name']}: {str(e)}", "ERROR")
                continue
        
        # If we haven't found the commit yet and no specific branch list was provided,
        # try checking remote branches
        if not commit_info and not branch_list:
            debug_log("Checking remote branches...")
            try:
                refs_url = f"/repos/{repo_name}/git/refs"
                refs = gh._make_request("GET", refs_url)
                for ref in refs:
                    if ref["ref"].startswith("refs/remotes/"):
                        branch_name = ref["ref"].replace("refs/remotes/", "")
                        try:
                            debug_log(f"Checking remote branch {branch_name}")
                            commits_url = f"/repos/{repo_name}/commits"
                            params = {"sha": branch_name, "per_page": 100}
                            page = 1
                            found = False
                            
                            while True:
                                params["page"] = page
                                commits = gh._make_request("GET", commits_url, params=params)
                                if not commits:
                                    break
                                    
                                for commit in commits:
                                    if commit["sha"] == commit_sha:
                                        debug_log(f"Found commit {commit_sha} in remote branch {branch_name}")
                                        # Found the commit! Store its info
                                        if not commit_info:
                                            commit_info = {
                                                "sha": commit["sha"],
                                                "message": commit["commit"]["message"],
                                                "author": commit["commit"]["author"]["name"],
                                                "date": commit["commit"]["author"]["date"]
                                            }
                                        
                                        # Add branch info
                                        branch_info = {
                                            "name": branch_name,
                                            "sha": ref["object"]["sha"],
                                            "protected": False,
                                            "type": "remote"
                                        }
                                        if not any(b["name"] == branch_name for b in containing_branches):
                                            containing_branches.append(branch_info)
                                        found = True
                                        break
                                
                                if found:
                                    break
                                page += 1
                                
                        except ValueError as e:
                            debug_log(f"Error checking remote branch {branch_name}: {str(e)}", "WARNING")
                            continue
                        except Exception as e:
                            debug_log(f"Unexpected error checking remote branch {branch_name}: {str(e)}", "ERROR")
                            continue
            except ValueError as e:
                debug_log(f"Error checking remote branches: {str(e)}", "WARNING")
            except Exception as e:
                debug_log(f"Unexpected error checking remote branches: {str(e)}", "ERROR")
        
        # If we still haven't found the commit, try one last direct lookup
        if not commit_info:
            debug_log("Attempting direct commit lookup...")
            try:
                commit = gh.get_commit(repo_name, commit_sha)
                commit_info = {
                    "sha": commit["sha"],
                    "message": commit["commit"]["message"],
                    "author": commit["commit"]["author"]["name"],
                    "date": commit["commit"]["author"]["date"]
                }
                debug_log(f"Found commit {commit_sha} through direct lookup")
            except ValueError as e:
                if "Resource not found" in str(e):
                    debug_log(f"Commit {commit_sha} not found in repository", "WARNING")
                    return json.dumps({
                        "error": f"Commit {commit_sha} not found in repository {repo_name}",
                        "details": "The commit might have been force-pushed or deleted"
                    })
                else:
                    debug_log(f"Error in direct commit lookup: {str(e)}", "ERROR")
                    raise
        
        if not commit_info:
            debug_log(f"Could not find commit {commit_sha} through any method", "WARNING")
            return json.dumps({
                "error": f"Could not find commit {commit_sha} in repository {repo_name}",
                "details": "The commit might exist but is not accessible through the API"
            })
        
        response_data = {
            "commit_sha": commit_sha,
            "repository": repo_name,
            "commit_message": commit_info["message"],
            "commit_author": commit_info["author"],
            "commit_date": commit_info["date"],
            "branches": containing_branches,
            "branch_count": len(containing_branches),
            "checked_branches": len(branches_to_check)
        }
        
        if branch_list:
            response_data["specified_branches"] = branch_list
            response_data["valid_branches_found"] = len(branches_to_check)
        
        debug_log(f"Successfully found {len(containing_branches)} branches containing commit {commit_sha}")
        return json.dumps(response_data, indent=2)
    
    except ValueError as e:
        error_msg = f"GitHub API error for commit {commit_sha}: {str(e)}"
        debug_log(error_msg, "ERROR")
        return json.dumps({
            "error": error_msg,
            "details": "This might be due to connection issues or API rate limits"
        })
    except Exception as e:
        error_msg = f"Unexpected error finding branches for commit {commit_sha}: {str(e)}"
        debug_log(error_msg, "ERROR")
        return json.dumps({
            "error": error_msg,
            "details": "An unexpected error occurred while processing the request"
        })

@mcp.tool()
async def get_commit_graph(ctx: Context, repo_name: str, starting_commit: str = None, 
                          max_commits: int = 100, traverse_direction: str = "forward") -> str:
    """
    Generate a JSON representation of the git commit graph for visualization.
    
    Args:
        repo_name: The full name of the repository (e.g., 'owner/repo')
        starting_commit: Optional SHA of a commit to use as starting point
        max_commits: Maximum number of commits to include (default: 100)
        traverse_direction: Direction to traverse from starting commit ('backward'=ancestors, 'forward'=descendants)
        
    Returns:
        A formatted JSON string with commit graph data
    """
    debug_log(f"Generating commit graph for {repo_name}, starting commit: {starting_commit}, max_commits: {max_commits}, direction: {traverse_direction}")
    try:
        # Validate input parameters
        if not repo_name or "/" not in repo_name:
            error_msg = f"Invalid repo_name format: {repo_name}. Expected format: 'owner/repo'"
            debug_log(error_msg, "ERROR")
            return json.dumps({"error": error_msg})
            
        # Get GitHub client from context
        gh = ctx.request_context.lifespan_context
        if not gh:
            error_msg = "GitHub client not found in context"
            debug_log(error_msg, "ERROR")
            return json.dumps({"error": error_msg})
            
        gh_token = gh.token
        if not gh_token:
            error_msg = "GitHub token not found"
            debug_log(error_msg, "ERROR")
            return json.dumps({"error": error_msg})
        
        # Parse owner and repo from repo_name
        owner, repo = repo_name.split('/')
        debug_log(f"Parsed owner: {owner}, repo: {repo}")
        
        # Create GraphQL headers
        graphql_headers = {
            'Authorization': f'Bearer {gh_token}',
            'Content-Type': 'application/json'
        }
        graphql_url = 'https://api.github.com/graphql'
        
        # Initialize data structures
        commits = {}
        branches = {}
        
        # First, fetch branch information
        debug_log(f"Fetching branch information for {repo_name}")
        branches = await _fetch_branches_graphql(owner, repo, graphql_url, graphql_headers)
        debug_log(f"Found {len(branches)} branches")
        
        # Fetch commits based on parameters
        if starting_commit:
            debug_log(f"Fetching commit history for {starting_commit} with {traverse_direction} traversal")
            commits = await _fetch_commit_history(owner, repo, starting_commit, graphql_url, 
                                                graphql_headers, traverse_direction, max_commits)
            debug_log(f"Found {len(commits)} commits in history")
        else:
            debug_log(f"Fetching most recent {max_commits} commits")
            commits = await _fetch_recent_commits(owner, repo, graphql_url, graphql_headers, max_commits)
            debug_log(f"Found {len(commits)} recent commits")
        
        if not commits:
            debug_log("No commits were found", "WARNING")
            
        # Build parent-child relationships
        debug_log("Building parent-child relationships between commits")
        for sha, commit_data in commits.items():
            if 'children' not in commit_data:
                commit_data['children'] = []
                
        for sha, commit_data in commits.items():
            for parent_sha in commit_data.get('parents', []):
                if parent_sha in commits:
                    commits[parent_sha]['children'].append(sha)
        
        # Create result data structure
        graph_data = {
            "repository": repo_name,
            "direction": "forward", # Default visualization direction
            "traverse_direction": traverse_direction,
            "commits": {},
            "branches": branches
        }
        
        # Add starting commit info if specified
        if starting_commit:
            if starting_commit in commits:
                debug_log(f"Adding starting commit info for {starting_commit}")
                graph_data["starting_commit"] = starting_commit
                graph_data["starting_commit_short"] = starting_commit[:7]
                containing_branches = await _find_branches_for_commit(
                    owner, repo, starting_commit, branches, graphql_url, graphql_headers
                )
                debug_log(f"Found {len(containing_branches)} branches containing starting commit")
                graph_data["branches_containing_starting_commit"] = containing_branches
            else:
                debug_log(f"Starting commit {starting_commit} not found in fetched commits", "WARNING")
        
        # Add commits to result
        for sha, commit_data in commits.items():
            short_sha = sha[:7]
            date = commit_data.get('date', '')
            if date and 'Z' in date:
                date = datetime.fromisoformat(date.replace('Z', '+00:00')).strftime('%Y-%m-%d')
            
            graph_data["commits"][sha] = {
                "short_sha": short_sha,
                "message": commit_data.get('message', ''),
                "date": date,
                "author": commit_data.get('author', 'Unknown'),
                "parents": commit_data.get('parents', []),
                "children": commit_data.get('children', [])
            }
        
        result_json = json.dumps(graph_data, indent=2)
        debug_log(f"Successfully generated commit graph with {len(commits)} commits")
        return result_json
    
    except Exception as e:
        log_exception(e, f"Error generating commit graph for {repo_name}")
        return json.dumps({"error": f"An error occurred: {str(e)}"})

@mcp.tool()
async def trace_commit_path(ctx: Context, repo_name: str, commit_hash: str, stop_at_branch: str = None) -> str:
    """
    Trace a commit's path through branches in a GitHub repository to understand its promotion history.
    
    Args:
        repo_name: The full name of the repository (e.g., 'owner/repo')
        commit_hash: The SHA of the commit to trace
        stop_at_branch: Optional name of a branch to stop tracing at (e.g., 'staging')
        
    Returns:
        A formatted JSON string with commit tracing information, including:
        - Which branches contain the commit
        - Pull requests that included the commit
        - The promotion path from branch to branch
    """
    debug_log(f"Tracing path for commit {commit_hash} in {repo_name}")
    try:
        # Parse owner and repo from repo_name
        parts = repo_name.split('/')
        if len(parts) != 2:
            return json.dumps({"error": "Invalid repository name format. Expected 'owner/repo'"})
            
        owner, repo = parts
        
        # Get GitHub client from context
        gh = ctx.request_context.lifespan_context
        if not gh:
            return json.dumps({"error": "GitHub client not available"})
        
        result = {
            "status": "success",
            "commit_hash": commit_hash,
            "owner": owner,
            "repo": repo,
            "formatted_output": []
        }
        
        # Check if this commit is directly from main
        debug_log(f"Checking if commit {commit_hash} is from main branch...")
        is_from_main = await is_commit_from_branch(gh, owner, repo, commit_hash, "main")
        
        if is_from_main:
            message = f"The commit {commit_hash} is from the MAIN branch only. No further traceability needed."
            debug_log(f"\n✅ {message}")
            result["is_from_main"] = True
            result["message"] = message
            result["formatted_output"] = [f"✅ {message}"]
            return json.dumps(result, indent=2)
        
        # Get commit details
        commit_data = gh.get_commit(repo_name, commit_hash)
        if not commit_data:
            return json.dumps({
                "status": "error",
                "message": f"Failed to get commit details for {commit_hash}"
            })
            
        debug_log(f"Commit: {commit_data['sha']}")
        if 'commit' in commit_data and 'author' in commit_data['commit']:
            author_name = commit_data['commit']['author'].get('name', 'Unknown')
            author_date = commit_data['commit']['author'].get('date', 'Unknown')
            commit_msg = commit_data['commit'].get('message', '')
            result["commit_info"] = {
                "author": author_name,
                "date": author_date,
                "message": commit_msg
            }
        
        # Get branches containing the commit
        branches = await get_branches_containing_commit(gh, owner, repo, commit_hash)
        if not branches:
            no_branches_msg = f"No branches contain the commit {commit_hash}"
            return json.dumps({
                "status": "warning",
                "message": no_branches_msg,
                "is_from_main": False,
                "formatted_output": [f"❌ {no_branches_msg}"]
            })
            
        debug_log("\n✅ Commit found in the following branches:")
        result["branches"] = branches
        branch_output = ["\n✅ Commit found in the following branches:"]
        for branch in branches:
            branch_line = f"  - {branch}"
            debug_log(branch_line)
            branch_output.append(branch_line)
        
        result["formatted_output"].extend(branch_output)
        
        # Define branches to check in the promotion workflow
        branches_to_check = ['develop', 'UAT', 'staging', 'main']
        if stop_at_branch and stop_at_branch in branches_to_check:
            # Only trace up to the specified branch
            stop_index = branches_to_check.index(stop_at_branch)
            branches_to_check = branches_to_check[:stop_index + 1]
        
        # Trace the commit through branches
        trace_result = await trace_commit_through_branches(gh, owner, repo, commit_hash, branches_to_check)
        if "formatted_output" in trace_result:
            result["formatted_output"].extend(trace_result["formatted_output"])
        
        result.update({k: v for k, v in trace_result.items() if k != "formatted_output"})
        
        # Look for PRs that contain this commit
        prs = await find_prs_containing_commit(gh, owner, repo, commit_hash)
        if prs:
            debug_log("\n📋 Pull Requests containing this commit:")
            pr_output = ["\n📋 Pull Requests containing this commit:"]
            for pr in prs:
                pr_line = f"  - PR #{pr['number']}: {pr['title']}"
                debug_log(pr_line)
                pr_output.append(pr_line)
                
                if 'url' in pr:
                    url_line = f"    URL: {pr['url']}"
                    debug_log(url_line)
                    pr_output.append(url_line)
                
                if pr.get("merged_at"):
                    merged_line = f"    Merged: {pr.get('merged_at')}"
                    debug_log(merged_line)
                    pr_output.append(merged_line)
            
            result["pull_requests"] = prs
            result["formatted_output"].extend(pr_output)
        else:
            no_pr_line = "\n📋 No Pull Requests directly contain this commit."
            debug_log(no_pr_line)
            result["formatted_output"].append(no_pr_line)
        
        return json.dumps(result, indent=2)
        
    except ValueError as e:
        log_exception(e, f"GitHub API error tracing commit {commit_hash}")
        return json.dumps({"error": str(e)})
    except Exception as e:
        log_exception(e, f"Error tracing commit {commit_hash}")
        return json.dumps({"error": f"An error occurred: {str(e)}"})

# Helper functions for trace_commit_path tool

async def is_commit_from_branch(gh, owner, repo, commit_hash, branch_name="main"):
    """Check if a commit originated directly in a specific branch"""
    repo_name = f"{owner}/{repo}"
    
    try:
        # Get the branch commit data
        branch_url = f"/repos/{repo_name}/branches/{branch_name}"
        branch_data = gh._make_request("GET", branch_url)
        if not branch_data:
            return False
            
        branch_commit = branch_data["commit"]["sha"]
        
        # Get commit details
        commit_data = gh.get_commit(repo_name, commit_hash)
        if not commit_data:
            return False
            
        # If the commit is not in the branch, it's not from this branch
        comparison_url = f"/repos/{repo_name}/compare/{commit_hash}...{branch_name}"
        comparison = gh._make_request("GET", comparison_url)
        if not comparison:
            return False
            
        if comparison.get("status") not in ["identical", "behind"]:
            debug_log(f"\nCommit is not in {branch_name} branch at all.")
            return False
        
        # Check if it's a merge commit (which likely didn't originate in this branch)
        origin_info = await analyze_commit_origin(gh, owner, repo, commit_hash)
        if not origin_info:
            return False
            
        if origin_info["is_merge"]:
            debug_log(f"\nCommit is in {branch_name} but is a merge commit.")
            if origin_info["source_branch"]:
                debug_log(f"   Likely originated from: {origin_info['source_branch']}")
            if origin_info["pr_number"]:
                debug_log(f"   Merged via PR #{origin_info['pr_number']}")
            return False
        
        # If commit is on the branch but not a merge commit, try to determine if it was made directly on the branch
        # Check if the commit was part of a PR
        search_url = "https://api.github.com/search/issues"
        params = {
            "q": f"type:pr repo:{owner}/{repo} {commit_hash}",
        }
        
        session = requests.Session()
        session.headers.update(gh.session.headers)
        response = session.get(search_url, params=params)
        
        if response.status_code != 200:
            # If we can't determine, assume it's from the branch
            return True
            
        pr_search = response.json()
        
        # If the commit appears in PRs, it likely didn't originate directly in this branch
        if pr_search.get("total_count", 0) > 0:
            debug_log(f"\nCommit is in {branch_name} but was introduced through a PR.")
            return False
        
        return True
    except Exception as e:
        debug_log(f"Error in is_commit_from_branch: {str(e)}")
        return False

async def analyze_commit_origin(gh, owner, repo, commit_hash):
    """Analyze where a commit likely originated from"""
    repo_name = f"{owner}/{repo}"
    
    try:
        commit_data = gh.get_commit(repo_name, commit_hash)
        if not commit_data:
            return None
            
        # Check if it's a merge commit (has multiple parents)
        parents = commit_data.get("parents", [])
        is_merge = len(parents) > 1
        
        # Get commit message
        commit_msg = commit_data.get("commit", {}).get("message", "")
        
        # Check for PR information
        pr_match = re.search(r"Merge pull request #(\d+)", commit_msg)
        pr_number = pr_match.group(1) if pr_match else None
        
        # Check for source branch information
        branch_match = re.search(r"from\s+(\S+)", commit_msg)
        source_branch = branch_match.group(1) if branch_match else None
        
        return {
            "is_merge": is_merge,
            "source_branch": source_branch,
            "pr_number": pr_number,
            "commit_message": commit_msg,
            "parent_count": len(parents),
            "parents": [p.get("sha") for p in parents]
        }
    except Exception as e:
        debug_log(f"Error in analyze_commit_origin: {str(e)}")
        return None

async def get_branches_containing_commit(gh, owner, repo, commit_hash):
    """Find all branches containing a specific commit"""
    repo_name = f"{owner}/{repo}"
    debug_log(f"Finding branches that contain commit {commit_hash}...")
    
    try:
        # Get all branches
        branches = gh.get_branches(repo_name)
        
        containing_branches = []
        
        # For each branch, check if the commit is in the history
        for branch in branches:
            branch_name = branch["name"]
            comparison_url = f"/repos/{repo_name}/compare/{commit_hash}...{branch_name}"
            
            try:
                comparison = gh._make_request("GET", comparison_url)
                if not comparison:
                    continue
                    
                status = comparison.get("status")
                
                # If status is "identical" or "behind", the commit is in this branch
                if status in ["identical", "behind"]:
                    containing_branches.append(branch_name)
            except Exception as e:
                debug_log(f"Error comparing {commit_hash} to {branch_name}: {str(e)}")
                # If we can't compare, skip this branch
                continue
        
        return containing_branches
    except Exception as e:
        debug_log(f"Error in get_branches_containing_commit: {str(e)}")
        return []

async def find_prs_containing_commit(gh, owner, repo, commit_hash):
    """Find PRs that contain a specific commit"""
    repo_name = f"{owner}/{repo}"
    
    try:
        search_url = "https://api.github.com/search/issues"
        params = {
            "q": f"type:pr repo:{owner}/{repo} {commit_hash}",
        }
        
        session = requests.Session()
        session.headers.update(gh.session.headers)
        response = session.get(search_url, params=params)
        
        if response.status_code != 200:
            debug_log(f"PR search failed: {response.status_code} - {response.text}")
            return []
            
        pr_search = response.json()
        
        prs = []
        for item in pr_search.get("items", []):
            # Get PR details to check if it's merged
            pr_url = f"/repos/{repo_name}/pulls/{item['number']}"
            try:
                pr_data = gh._make_request("GET", pr_url)
                merged_at = pr_data.get("merged_at") if pr_data else None
            except:
                merged_at = None
                
            prs.append({
                "number": item["number"],
                "title": item["title"],
                "url": item["html_url"],
                "state": item["state"],
                "merged_at": merged_at
            })
        
        return prs
    except Exception as e:
        debug_log(f"Error in find_prs_containing_commit: {str(e)}")
        return []

async def trace_commit_through_branches(gh, owner, repo, commit_hash, branches_to_check=None):
    """Trace a commit through different branches"""
    repo_name = f"{owner}/{repo}"
    
    if branches_to_check is None:
        branches_to_check = ['develop', 'UAT', 'staging', 'main']
            
    debug_log(f"\nTracing commit {commit_hash} through branches...\n")
    
    try:
        branch_info = {}
        
        # First, check if the commit is in each branch
        for branch in branches_to_check:
            comparison_url = f"/repos/{repo_name}/compare/{commit_hash}...{branch}"
            try:
                comparison = gh._make_request("GET", comparison_url)
                if not comparison:
                    branch_info[branch] = {"contains": False, "error": True}
                    continue
                    
                status = comparison.get("status")
                
                if status in ["identical", "behind"]:
                    branch_info[branch] = {"contains": True}
                else:
                    branch_info[branch] = {"contains": False}
            except Exception as e:
                debug_log(f"Error comparing {commit_hash} to {branch}: {str(e)}")
                branch_info[branch] = {"contains": False, "error": True}
        
        # Find PRs that brought the commit into each branch
        prs = await find_prs_containing_commit(gh, owner, repo, commit_hash)
        for pr in prs:
            pr_url = f"/repos/{repo_name}/pulls/{pr['number']}"
            pr_data = gh._make_request("GET", pr_url)
            if not pr_data:
                continue
                
            base_branch = pr_data.get("base", {}).get("ref")
            head_branch = pr_data.get("head", {}).get("ref")
            
            if base_branch in branch_info:
                if "prs" not in branch_info[base_branch]:
                    branch_info[base_branch]["prs"] = []
                
                branch_info[base_branch]["prs"].append({
                    "number": pr["number"],
                    "title": pr["title"],
                    "source_branch": head_branch,
                    "merged_at": pr.get("merged_at")
                })
        
        # Build formatted output
        formatted_output = [f"📈 Tracing commit {commit_hash} through branches...\n"]
        
        # Track where the commit entered through PRs
        branch_prs = {}
        
        # Print the results
        for branch in branches_to_check:
            info = branch_info.get(branch, {})
            if info.get("contains", False):
                branch_line = f"  ✅ {branch.upper()}: Contains the commit"
                formatted_output.append(branch_line)
                
                # Check if we know which PR brought it in
                if "prs" in info:
                    for pr in info["prs"]:
                        pr_line = f"     🚀 PR #{pr['number']} from {pr['source_branch']}: {pr['title']}"
                        formatted_output.append(pr_line)
                        
                        # Save PR info for summary
                        branch_prs[branch] = (pr['number'], pr.get('source_branch', ''), len(formatted_output) - 1)
                        
                        if pr.get("merged_at"):
                            merged_date = datetime.fromisoformat(pr["merged_at"].replace('Z', '+00:00'))
                            date_line = f"        Merged on: {merged_date.strftime('%Y-%m-%d %H:%M:%S')}"
                            formatted_output.append(date_line)
            else:
                error_msg = " (Error checking)" if info.get("error", False) else ""
                branch_line = f"  ❌ {branch.upper()}: Does not contain the commit{error_msg}"
                formatted_output.append(branch_line)
        
        # Add summary if we found PRs
        if branch_prs:
            summary = "\n✅ The commit was merged to branches through these PRs:"
            formatted_output.append(summary)
            for branch in branches_to_check:
                if branch in branch_prs:
                    pr_num = branch_prs[branch][0]
                    summary_line = f"  - {branch.upper()}: PR #{pr_num}"
                    formatted_output.append(summary_line)
        
        return {
            "status": "success",
            "branches": branch_info,
            "branch_prs": branch_prs,
            "formatted_output": formatted_output
        }
    except Exception as e:
        debug_log(f"Error in trace_commit_through_branches: {str(e)}")
        return {
            "status": "error",
            "message": f"An error occurred tracing through branches: {str(e)}",
            "formatted_output": [f"❌ Error tracing through branches: {str(e)}"]
        }

# Helper functions for get_commit_graph tool

async def _fetch_branches_graphql(owner: str, repo: str, graphql_url: str, 
                                 graphql_headers: Dict[str, str]) -> Dict[str, str]:
    """Fetch branches using GraphQL API"""
    branches = {}
    debug_log(f"Fetching branches for {owner}/{repo} using GraphQL")
    query = """
    query ($owner: String!, $repo: String!, $cursor: String) {
      repository(owner: $owner, name: $repo) {
        refs(first: 100, refPrefix: "refs/heads/", after: $cursor) {
          pageInfo {
            hasNextPage
            endCursor
          }
          nodes {
            name
            target {
              ... on Commit {
                oid
              }
            }
          }
        }
      }
    }
    """
    
    variables = {
        "owner": owner,
        "repo": repo,
        "cursor": None
    }
    
    has_next_page = True
    total_branches = 0
    
    while has_next_page:
        try:
            debug_log(f"Making GraphQL request for branches, cursor: {variables['cursor']}")
            response = requests.post(
                graphql_url,
                headers=graphql_headers,
                json={"query": query, "variables": variables}
            )
            response.raise_for_status()
            result = response.json()
            
            if "errors" in result:
                debug_log(f"GraphQL Error: {result['errors']}", "ERROR")
                break
            
            data = result.get("data", {}).get("repository", {}).get("refs", {})
            if not data:
                debug_log("No repository or refs data returned from GraphQL", "ERROR")
                break
                
            page_info = data.get("pageInfo", {})
            branch_nodes = data.get("nodes", [])
            
            branch_count = len(branch_nodes)
            debug_log(f"Retrieved {branch_count} branches in this page")
            
            for branch in branch_nodes:
                if branch.get("name") and branch.get("target", {}).get("oid"):
                    branches[branch["name"]] = branch["target"]["oid"]
                else:
                    debug_log(f"Skipped invalid branch data: {branch}", "WARNING")
            
            total_branches += branch_count
            has_next_page = page_info.get("hasNextPage", False)
            variables["cursor"] = page_info.get("endCursor") if has_next_page else None
        
        except Exception as e:
            log_exception(e, "Error fetching branches")
            break
    
    debug_log(f"Completed fetching {total_branches} branches, returning {len(branches)} valid branches")
    return branches

async def _fetch_recent_commits(owner: str, repo: str, graphql_url: str, 
                              graphql_headers: Dict[str, str], max_commits: int) -> Dict[str, Dict]:
    """Fetch recent commits using GraphQL API"""
    commits = {}
    debug_log(f"Fetching up to {max_commits} recent commits for {owner}/{repo}")
    query = """
    query ($owner: String!, $repo: String!, $cursor: String) {
      repository(owner: $owner, name: $repo) {
        defaultBranchRef {
          target {
            ... on Commit {
              history(first: 100, after: $cursor) {
                pageInfo {
                  hasNextPage
                  endCursor
                }
                nodes {
                  oid
                  messageHeadline
                  committedDate
                  author {
                    name
                  }
                  parents(first: 10) {
                    nodes {
                      oid
                    }
                  }
                }
              }
            }
          }
        }
      }
    }
    """
    
    variables = {
        "owner": owner,
        "repo": repo,
        "cursor": None
    }
    
    has_next_page = True
    commit_count = 0
    
    while has_next_page and commit_count < max_commits:
        try:
            debug_log(f"Making GraphQL request for recent commits, cursor: {variables['cursor']}")
            response = requests.post(
                graphql_url,
                headers=graphql_headers,
                json={"query": query, "variables": variables}
            )
            response.raise_for_status()
            result = response.json()
            
            if "errors" in result:
                debug_log(f"GraphQL Error: {result['errors']}", "ERROR")
                break
            
            repository = result.get("data", {}).get("repository", {})
            if not repository:
                debug_log("No repository data returned from GraphQL", "ERROR")
                break
                
            default_branch_ref = repository.get("defaultBranchRef", {})
            if not default_branch_ref:
                debug_log("No default branch reference found", "ERROR")
                break
                
            target = default_branch_ref.get("target", {})
            if not target:
                debug_log("No target found for default branch", "ERROR")
                break
                
            history = target.get("history", {})
            if not history:
                debug_log("No history found for default branch", "ERROR")
                break
                
            page_info = history.get("pageInfo", {})
            commit_nodes = history.get("nodes", [])
            
            page_commit_count = len(commit_nodes)
            debug_log(f"Retrieved {page_commit_count} commits in this page")
            
            for commit in commit_nodes:
                sha = commit.get("oid")
                if not sha:
                    debug_log(f"Skipping commit with no SHA", "WARNING")
                    continue
                    
                if sha not in commits and commit_count < max_commits:
                    parent_shas = [p.get("oid") for p in commit.get("parents", {}).get("nodes", []) if p.get("oid")]
                    commits[sha] = {
                        'message': commit.get("messageHeadline", ""),
                        'parents': parent_shas,
                        'date': commit.get("committedDate", ""),
                        'author': commit.get("author", {}).get("name", "Unknown"),
                        'children': []
                    }
                    commit_count += 1
            
            has_next_page = page_info.get("hasNextPage", False) and commit_count < max_commits
            variables["cursor"] = page_info.get("endCursor") if has_next_page else None
        
        except Exception as e:
            log_exception(e, "Error fetching recent commits")
            break
    
    debug_log(f"Completed fetching {commit_count} recent commits")
    return commits

async def _fetch_commit_history(owner: str, repo: str, starting_commit: str, graphql_url: str,
                              graphql_headers: Dict[str, str], traverse_direction: str, 
                              max_depth: int) -> Dict[str, Dict]:
    """Fetch commit history from a starting point"""
    commits = {}
    debug_log(f"Fetching commit history for {owner}/{repo} starting from {starting_commit}, direction: {traverse_direction}, max depth: {max_depth}")
    
    # First, get the starting commit
    query = """
    query ($owner: String!, $repo: String!, $sha: GitObjectID!) {
      repository(owner: $owner, name: $repo) {
        object(oid: $sha) {
          ... on Commit {
            oid
            messageHeadline
            committedDate
            author {
              name
            }
            parents(first: 10) {
              nodes {
                oid
              }
            }
          }
        }
      }
    }
    """
    
    variables = {
        "owner": owner,
        "repo": repo,
        "sha": starting_commit
    }
    
    try:
        debug_log(f"Making GraphQL request to fetch starting commit {starting_commit}")
        response = requests.post(
            graphql_url,
            headers=graphql_headers,
            json={"query": query, "variables": variables}
        )
        response.raise_for_status()
        result = response.json()
        
        if "errors" in result:
            debug_log(f"GraphQL Error: {result['errors']}", "ERROR")
            return commits
            
        repository_object = result.get("data", {}).get("repository", {}).get("object")
        if not repository_object:
            debug_log(f"Commit {starting_commit} not found in repository", "ERROR")
            return commits
        
        # Add the starting commit
        commit = repository_object
        sha = commit.get("oid")
        if not sha:
            debug_log("Starting commit has no SHA", "ERROR")
            return commits
            
        parent_shas = [p.get("oid") for p in commit.get("parents", {}).get("nodes", []) if p.get("oid")]
        commits[sha] = {
            'message': commit.get("messageHeadline", ""),
            'parents': parent_shas,
            'date': commit.get("committedDate", ""),
            'author': commit.get("author", {}).get("name", "Unknown"),
            'children': []
        }
        debug_log(f"Added starting commit {sha} with {len(parent_shas)} parents")
        
        # For backward traversal (ancestors)
        if traverse_direction.lower() == "backward":
            debug_log("Performing backward traversal to find ancestors")
            commits_to_process = parent_shas.copy()
            processed_commits = {sha}
            depth = 0
            
            while commits_to_process and depth < max_depth and len(commits) < max_depth:
                current_sha = commits_to_process.pop(0)
                debug_log(f"Processing ancestor commit: {current_sha}")
                
                if current_sha in processed_commits:
                    debug_log(f"Skipping already processed commit: {current_sha}")
                    continue
                
                # Fetch this commit
                variables["sha"] = current_sha
                response = requests.post(
                    graphql_url,
                    headers=graphql_headers,
                    json={"query": query, "variables": variables}
                )
                
                if response.status_code == 200:
                    result = response.json()
                    repository_object = result.get("data", {}).get("repository", {}).get("object")
                    
                    if repository_object:
                        commit = repository_object
                        parent_shas = [p.get("oid") for p in commit.get("parents", {}).get("nodes", []) if p.get("oid")]
                        
                        commits[current_sha] = {
                            'message': commit.get("messageHeadline", ""),
                            'parents': parent_shas,
                            'date': commit.get("committedDate", ""),
                            'author': commit.get("author", {}).get("name", "Unknown"),
                            'children': []
                        }
                        
                        debug_log(f"Added ancestor commit {current_sha} with {len(parent_shas)} parents")
                        processed_commits.add(current_sha)
                        
                        # Add parents to processing queue
                        for parent in parent_shas:
                            if parent not in processed_commits and parent not in commits_to_process:
                                debug_log(f"Adding parent {parent} to processing queue")
                                commits_to_process.append(parent)
                    else:
                        debug_log(f"Commit {current_sha} not found in repository", "WARNING")
                else:
                    debug_log(f"Failed to fetch commit {current_sha}: {response.status_code}", "ERROR")
                
                depth += 1
            
            debug_log(f"Completed backward traversal with {len(commits)} commits")
            
        # For forward traversal (descendants), we need to get more commits first
        elif traverse_direction.lower() == "forward":
            debug_log("Performing forward traversal to find descendants")
            # For forward traversal, we need to fetch more commits to find potential descendants
            debug_log("Fetching recent commits to identify potential descendants")
            recent_commits = await _fetch_recent_commits(owner, repo, graphql_url, graphql_headers, 500)
            
            # Add recent commits to our commits dictionary
            for sha, commit_data in recent_commits.items():
                if sha not in commits:
                    commits[sha] = commit_data
            
            debug_log(f"Building parent-child relationships among {len(commits)} commits")
            # Build parent-child relationships
            for sha, commit_data in commits.items():
                for parent_sha in commit_data.get('parents', []):
                    if parent_sha in commits and sha not in commits[parent_sha].get('children', []):
                        if 'children' not in commits[parent_sha]:
                            commits[parent_sha]['children'] = []
                        commits[parent_sha]['children'].append(sha)
            
            # Now traverse forward from the starting commit
            commits_to_keep = {starting_commit}
            if starting_commit in commits and 'children' in commits[starting_commit]:
                commits_to_process = list(commits[starting_commit]['children'])
                debug_log(f"Starting forward traversal with {len(commits_to_process)} direct descendants")
            else:
                debug_log("No direct descendants found for starting commit", "WARNING")
                commits_to_process = []
                
            processed = {starting_commit}
            depth = 0
            
            while commits_to_process and depth < max_depth:
                current_sha = commits_to_process.pop(0)
                debug_log(f"Processing descendant commit: {current_sha}")
                
                if current_sha in processed:
                    debug_log(f"Skipping already processed commit: {current_sha}")
                    continue
                
                processed.add(current_sha)
                commits_to_keep.add(current_sha)
                
                # Add children to processing queue
                if current_sha in commits:
                    children = commits[current_sha].get('children', [])
                    debug_log(f"Found {len(children)} children for commit {current_sha}")
                    for child_sha in children:
                        if child_sha not in processed and child_sha not in commits_to_process:
                            debug_log(f"Adding child {child_sha} to processing queue")
                            commits_to_process.append(child_sha)
                
                depth += 1
            
            # Filter commits to keep only descendants and starting commit
            debug_log(f"Filtering to keep only starting commit and its descendants ({len(commits_to_keep)} commits)")
            commits = {sha: commit_data for sha, commit_data in commits.items() if sha in commits_to_keep}
            debug_log(f"Completed forward traversal with {len(commits)} commits")
    
    except Exception as e:
        log_exception(e, f"Error fetching commit history for {starting_commit}")
    
    return commits

async def _find_branches_for_commit(owner: str, repo: str, commit_sha: str, branches: Dict[str, str],
                                   graphql_url: str, graphql_headers: Dict[str, str]) -> List[str]:
    """Find branches that contain a specific commit"""
    debug_log(f"Finding branches containing commit {commit_sha}")
    # Direct branch reference (commit is branch head)
    direct_branches = [name for name, sha in branches.items() if sha == commit_sha]
    if direct_branches:
        debug_log(f"Found {len(direct_branches)} branches directly pointing to commit {commit_sha}")
        return direct_branches
    
    # Check a reasonable number of branches
    branches_to_check = list(branches.keys())
    if len(branches_to_check) > 10:
        debug_log(f"Too many branches ({len(branches_to_check)}), filtering to prioritize common ones")
        # Prioritize common branch names
        priority_branches = ['main', 'master', 'develop', 'dev', 'release']
        filtered_branches = [b for b in branches_to_check if any(p in b.lower() for p in priority_branches)]
        debug_log(f"Found {len(filtered_branches)} priority branches")
        # Add some other branches (limit to 15 total)
        remaining_slots = 15 - len(filtered_branches)
        if remaining_slots > 0:
            filtered_branches.extend([b for b in branches_to_check if b not in filtered_branches][:remaining_slots])
        branches_to_check = filtered_branches
    
    debug_log(f"Checking {len(branches_to_check)} branches for commit {commit_sha}")
    containing_branches = []
    query = """
    query ($owner: String!, $repo: String!, $commitOid: GitObjectID!, $branchName: String!) {
      repository(owner: $owner, name: $repo) {
        ref(qualifiedName: $branchName) {
          compare(headRef: $branchName, baseOid: $commitOid) {
            status
          }
        }
      }
    }
    """
    
    for branch_name in branches_to_check:
        variables = {
            "owner": owner,
            "repo": repo,
            "commitOid": commit_sha,
            "branchName": f"refs/heads/{branch_name}"
        }
        
        try:
            debug_log(f"Checking if branch '{branch_name}' contains commit {commit_sha}")
            response = requests.post(
                graphql_url,
                headers=graphql_headers,
                json={"query": query, "variables": variables}
            )
            
            if response.status_code == 200:
                data = response.json()
                ref_data = data.get("data", {}).get("repository", {}).get("ref", {})
                if ref_data and ref_data.get("compare", {}).get("status") in ["AHEAD", "IDENTICAL"]:
                    debug_log(f"Branch '{branch_name}' contains commit {commit_sha}")
                    containing_branches.append(branch_name)
        except Exception as e:
            log_exception(e, f"Error checking if branch {branch_name} contains commit {commit_sha}")
    
    debug_log(f"Found {len(containing_branches)} branches containing commit {commit_sha}")
    return containing_branches

if __name__ == "__main__":
    debug_log("Starting GitHub MCP server")
    mcp.run()

"""
GitHub MCP Server

This server provides tools to analyze GitHub repositories, with a focus on
pull requests and commits. It helps answer questions like:

- What commits are in a specific pull request?
- Who authored the commits and when?
- What are the details of a specific commit?
- What pull requests exist in a repository?
- What are the PR statistics for a repository?
- What branches contain a specific commit?
- How has a commit been promoted through branches?

Usage:
1. Set GITHUB_PERSONAL_ACCESS_TOKEN or GITHUB_TOKEN environment variable
2. Run this script to start the server
3. Use with any MCP client like Claude or other AI tools

API Tools:
- find_pr_commits: Get all commits for a specific PR with author and timestamp info
- list_repo_pull_requests: Get PRs in a repository with filtering options
- get_commit_details: Get detailed information about a specific commit
- get_pr_stats: Get statistics about PRs in a repository over a time period
- get_pr_approvals: Get approval events and review information for a pull request
- find_commit_branches: Find branches that contain a specific commit
- get_commit_graph: Generate a visualization-ready JSON of the commit graph
- trace_commit_path: Trace a commit's path through branches to understand its promotion history
"""
