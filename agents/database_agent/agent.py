import asyncio
import os
import logging
import time
from datetime import datetime
from typing import Optional, Dict, Any
from google.adk.agents import Agent
from google.adk.tools.mcp_tool.mcp_toolset import MCPToolset, StdioServerParameters
from google.adk.agents.callback_context import CallbackContext
from google.adk.models import LlmRequest, LlmResponse
from google.adk.events import Event, EventActions
from google.genai.types import Content, Part
from dotenv import load_dotenv

# Load environment variables from the root .env file
load_dotenv(dotenv_path=os.path.join(os.path.dirname(__file__), '..', '..', '.env'))

database_url = os.getenv("DATABASE_URL")

# Configure structured logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler('database_agent.log')
    ]
)
logger = logging.getLogger('database_agent')

class DatabaseAgentState:
    """Manages state for the database agent"""

    def __init__(self):
        self.connection_status = "disconnected"
        self.tools_discovered = 0
        self.queries_executed = 0
        self.last_query_time = None
        self.errors_count = 0
        self.analysis_progress = {}

    def to_dict(self) -> Dict[str, Any]:
        """Convert state to dictionary for session storage"""
        return {
            "connection_status": self.connection_status,
            "tools_discovered": self.tools_discovered,
            "queries_executed": self.queries_executed,
            "last_query_time": self.last_query_time.isoformat() if self.last_query_time else None,
            "errors_count": self.errors_count,
            "analysis_progress": self.analysis_progress
        }

    def from_dict(self, data: Dict[str, Any]):
        """Load state from dictionary"""
        self.connection_status = data.get("connection_status", "disconnected")
        self.tools_discovered = data.get("tools_discovered", 0)
        self.queries_executed = data.get("queries_executed", 0)
        if data.get("last_query_time"):
            self.last_query_time = datetime.fromisoformat(data["last_query_time"])
        self.errors_count = data.get("errors_count", 0)
        self.analysis_progress = data.get("analysis_progress", {})

# Global state instance
agent_state = DatabaseAgentState()

def log_agent_event(level: str, message: str, **kwargs):
    """Structured logging for agent events"""
    extra_info = " | ".join([f"{k}={v}" for k, v in kwargs.items()])
    full_message = f"{message} | {extra_info}" if extra_info else message

    if level.upper() == "DEBUG":
        logger.debug(full_message)
    elif level.upper() == "INFO":
        logger.info(full_message)
    elif level.upper() == "WARNING":
        logger.warning(full_message)
    elif level.upper() == "ERROR":
        logger.error(full_message)

def before_agent_callback(callback_context: CallbackContext, user_message: Content) -> Optional[Content]:
    """Callback executed before agent starts processing"""
    start_time = time.time()

    log_agent_event("INFO", "Agent execution started",
                   agent_name=callback_context.agent_name,
                   session_id=callback_context.session.id if callback_context.session else "unknown",
                   user_message_length=len(str(user_message)))

    # Update session state
    if callback_context.session:
        callback_context.session.state["db_agent:execution_start"] = start_time
        callback_context.session.state["db_agent:state"] = agent_state.to_dict()

    return None  # Allow normal execution

def after_agent_callback(callback_context: CallbackContext, agent_response: Content) -> Optional[Content]:
    """Callback executed after agent completes processing"""
    end_time = time.time()

    # Calculate execution time
    start_time = callback_context.session.state.get("db_agent:execution_start", end_time) if callback_context.session else end_time
    execution_time = end_time - start_time

    log_agent_event("INFO", "Agent execution completed",
                   agent_name=callback_context.agent_name,
                   execution_time_seconds=round(execution_time, 2),
                   response_length=len(str(agent_response)))

    # Update session state with completion metrics
    if callback_context.session:
        callback_context.session.state["db_agent:execution_time"] = execution_time
        callback_context.session.state["db_agent:last_completion"] = end_time
        callback_context.session.state["db_agent:state"] = agent_state.to_dict()

    return None  # Use original response

def before_model_callback(callback_context: CallbackContext, llm_request: LlmRequest) -> Optional[LlmResponse]:
    """Callback executed before LLM model call"""
    log_agent_event("DEBUG", "LLM call initiated",
                   agent_name=callback_context.agent_name,
                   model=llm_request.model,
                   content_length=len(str(llm_request.contents)))

    # Update session state
    if callback_context.session:
        callback_context.session.state["db_agent:llm_calls"] = callback_context.session.state.get("db_agent:llm_calls", 0) + 1

    return None  # Allow normal LLM call

def after_model_callback(callback_context: CallbackContext, llm_response: LlmResponse) -> Optional[LlmResponse]:
    """Callback executed after LLM model call"""
    log_agent_event("DEBUG", "LLM call completed",
                   agent_name=callback_context.agent_name,
                   response_length=len(str(llm_response.content)))

    return None  # Use original response

def before_tool_callback(callback_context: CallbackContext, tool_name: str, tool_args: Dict[str, Any]) -> Optional[Dict[str, Any]]:
    """Callback executed before tool execution"""
    log_agent_event("INFO", "Tool execution started",
                   agent_name=callback_context.agent_name,
                   tool_name=tool_name,
                   args_count=len(tool_args))

    # Track database queries specifically
    if "query" in tool_name.lower() or "postgres" in tool_name.lower():
        agent_state.queries_executed += 1
        agent_state.last_query_time = datetime.now()

        # Update session state
        if callback_context.session:
            callback_context.session.state["db_agent:queries_executed"] = agent_state.queries_executed
            callback_context.session.state["db_agent:last_query_time"] = agent_state.last_query_time.isoformat()

    return None  # Allow normal tool execution

def after_tool_callback(callback_context: CallbackContext, tool_name: str, tool_result: Dict[str, Any]) -> Optional[Dict[str, Any]]:
    """Callback executed after tool execution"""
    success = not tool_result.get("error", False)

    log_agent_event("INFO", "Tool execution completed",
                   agent_name=callback_context.agent_name,
                   tool_name=tool_name,
                   success=success,
                   result_size=len(str(tool_result)))

    if not success:
        agent_state.errors_count += 1
        log_agent_event("ERROR", "Tool execution failed",
                       tool_name=tool_name,
                       error=tool_result.get("error", "Unknown error"))

    # Update session state
    if callback_context.session:
        callback_context.session.state["db_agent:tool_executions"] = callback_context.session.state.get("db_agent:tool_executions", 0) + 1
        callback_context.session.state["db_agent:errors_count"] = agent_state.errors_count

    return None  # Use original result

async def get_tools_async():
    """Connects to the postgres MCP server and returns the tools and exit stack."""
    log_agent_event("INFO", "Attempting to connect to postgres MCP server")

    try:
        # Check if npx is available (basic check)
        await asyncio.create_subprocess_shell('npx --version', stdout=asyncio.subprocess.PIPE, stderr=asyncio.subprocess.PIPE)

        tools, exit_stack = await MCPToolset.from_server(
            connection_params=StdioServerParameters(
                command='npx',
                args=['-y', '@modelcontextprotocol/server-postgres', database_url],
            )
        )

        # Update agent state
        agent_state.connection_status = "connected"
        agent_state.tools_discovered = len(tools)

        log_agent_event("INFO", "Successfully connected to postgres server",
                       tools_discovered=len(tools))

        # Log discovered tool names
        for tool in tools:
            log_agent_event("DEBUG", "Discovered tool", tool_name=tool.name)

        return tools, exit_stack

    except FileNotFoundError:
        agent_state.connection_status = "failed_npx_not_found"
        agent_state.errors_count += 1

        log_agent_event("ERROR", "npx command not found - please install npx: npm install -g npx")

        # Return empty tools and a no-op exit stack to prevent agent failure
        class DummyExitStack:
            async def __aenter__(self): return self
            async def __aexit__(self, *args): pass
        return [], DummyExitStack()

    except Exception as e:
        agent_state.connection_status = "failed_connection_error"
        agent_state.errors_count += 1

        log_agent_event("ERROR", "Failed to connect to postgres server", error=str(e))

        # Return empty tools and a no-op exit stack
        class DummyExitStack:
            async def __aenter__(self): return self
            async def __aexit__(self, *args): pass
        return [], DummyExitStack()


async def create_agent():
    """Creates the agent instance after fetching tools from the MCP server."""
    log_agent_event("INFO", "Creating database agent instance")

    tools, exit_stack = await get_tools_async()

    if not tools:
        log_agent_event("WARNING", "No tools discovered from MCP server - agent will lack database functionality")
    else:
        log_agent_event("INFO", "Agent created successfully", tools_count=len(tools))

    agent_instance = Agent(
        name="async_database_agent",
        description="A database agent with enhanced logging and state management that has access to a postgres database using an external MCP postgres tool.",
        model="gemini-2.5-flash-preview-04-17", # Ensure API key is in .env

        # Add callbacks for enhanced observability
        before_agent_callback=before_agent_callback,
        after_agent_callback=after_agent_callback,
        before_model_callback=before_model_callback,
        after_model_callback=after_model_callback,
        before_tool_callback=before_tool_callback,
        after_tool_callback=after_tool_callback,

        instruction="""
            You are a database analysis agent that helps users understand their database metrics and lead time for change data. You can:

            1. Connect to PostgreSQL databases
            2. Analyze lead time for change metrics
            3. Generate visualizations of the data
            4. Provide insights about the data

            When analyzing data, always:
            - Verify data completeness before analysis
            - Provide clear explanations of your findings
            - Use appropriate visualizations when needed
            - Handle errors gracefully

            For database queries:
            - Always use parameterized queries for security
            - Handle connection errors appropriately
            - Validate query results before processing

            For lead time analysis:
            - Calculate metrics accurately
            - Consider outliers and anomalies
            - Provide context for the numbers

            When generating visualizations:
            - Use appropriate chart types
            - Include clear labels and titles
            - Save plots in a consistent format

            Remember to:
            - Be thorough in your analysis
            - Provide actionable insights
            - Explain your methodology
            - Handle edge cases appropriately

            PostgreSQL Case Sensitivity Rules:
            - Table and column names containing uppercase letters MUST be quoted with double quotes
            - Example: Use "Commit" instead of commit, "prNumber" instead of prnumber
            - This is because PostgreSQL treats unquoted identifiers as lowercase by default
            - When identifiers are created with double quotes, they preserve their case sensitivity
            - Always use double quotes for:
            * Table names with uppercase letters (e.g., "Commit", "PullRequest")
            * Column names with uppercase letters (e.g., "prNumber", "estimatedOriginalTimestamp")
            - Example query:
            ```sql
            SELECT c."prNumber", c."estimatedOriginalTimestamp"
            FROM "Commit" c
            WHERE c."prNumber" = 35;
            ```
            ---
            IMPORTANT: If you see an error like 'column "prNumber" does not exist', it means the column name was not quoted and PostgreSQL is looking for a lowercase version. Always use double quotes for any table or column name that is not all-lowercase.

            Tip: To check the exact column names and their case, refer to the schema below or run this query in your database:
            SELECT column_name FROM information_schema.columns WHERE table_name = '<TableName>';

            If you get a column-not-found error, double-check your identifier quoting!

            ---
            Database Schema Reference (public schema):

            Table: "PullRequest"
            - "createdAt": timestamp without time zone
            - "number": integer
            - "mergedAt": timestamp without time zone
            - "baseBranch": text
            - "repo": text
            - "title": text
            - "author": text

            Table: "Commit"
            - "isMergeCommit": boolean
            - "createdAt": timestamp without time zone
            - "updatedAt": timestamp without time zone
            - "estimatedOriginalTimestamp": timestamp without time zone
            - "isSquashed": boolean
            - "authorDate": timestamp without time zone
            - "isRebased": boolean
            - "timestamp": timestamp without time zone
            - "prNumber": integer
            - "source": USER-DEFINED
            - "sha": text
            - "originalCommitSha": text
            - "message": text
            - "author": text
            - "repository": text
            - "id": text

            Table: "Metrics"
            - "developmentTimeHours": double precision
            - "prNumber": integer
            - "endToEndLtcHours": double precision
            - "nestedCommitCount": integer
            - "firstCommitTime": timestamp without time zone
            - "deployedAt": timestamp without time zone
            - "ltcHours": double precision
            - "createdAt": timestamp without time zone
            - "commitSha": text
            - "id": text

            ---
            Identifier Quoting Guidance:
            - Always use double quotes for table and column names with uppercase or camelCase letters.
            - Example: Use "prNumber" not prNumber, "Commit" not Commit.
            - Example query:
            SELECT * FROM "PullRequest" WHERE "number" = 3;
            SELECT c."sha", c."message", c."author", c."timestamp" FROM "Commit" c WHERE c."prNumber" = 35;

            Refer to this schema and quoting guidance for all queries to avoid errors.
            """,
        tools=tools,
    )

    return agent_instance, exit_stack

root_agent = create_agent()


