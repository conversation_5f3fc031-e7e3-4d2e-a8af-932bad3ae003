import asyncio
import os
from google.adk.agents import Agent
from google.adk.tools.mcp_tool.mcp_toolset import MCPToolset, StdioServerParameters
from dotenv import load_dotenv

# Load environment variables from the root .env file
load_dotenv(dotenv_path=os.path.join(os.path.dirname(__file__), '..', '..', '.env'))

database_url = os.getenv("DATABASE_URL")

async def get_tools_async():
    """Connects to the mcp-reddit server via uvx and returns the tools and exit stack."""
    print("--- Attempting to start and connect to mcp-reddit MCP server via uvx ---")
    try:
        # Check if npx is available (basic check)
        # A more robust check might involve checking the actual command's success
        await asyncio.create_subprocess_shell('npx --version', stdout=asyncio.subprocess.PIPE, stderr=asyncio.subprocess.PIPE)

        tools, exit_stack = await MCPToolset.from_server(
            connection_params=StdioServerParameters(
                command='npx',
                args=['-y', '@modelcontextprotocol/server-postgres', database_url],
            )
        )

        print(f"--- Successfully connected to postgres server. Discovered {len(tools)} tool(s). ---")
        # Print discovered tool names for debugging/instruction refinement
        for tool in tools:
            print(f"  - Discovered tool: {tool.name}") # Tool name is likely 'fetch_reddit_hot_threads' or similar
        return tools, exit_stack
    except FileNotFoundError:
        print("!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!")
        print("!!! ERROR: 'npx' command not found. Please install npx: npm install -g npx !!!")
        print("!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!")
        # Return empty tools and a no-op exit stack to prevent agent failure
        class DummyExitStack:
            async def __aenter__(self): return self
            async def __aexit__(self, *args): pass
        return [], DummyExitStack()
    except Exception as e:
        print(f"--- ERROR connecting to or starting postgres server: {e} ---")
        # Return empty tools and a no-op exit stack
        class DummyExitStack:
            async def __aenter__(self): return self
            async def __aexit__(self, *args): pass
        return [], DummyExitStack()


async def create_agent():
    """Creates the agent instance after fetching tools from the MCP server."""
    tools, exit_stack = await get_tools_async()
    discovered_tool_name = "query_postgres"
    if not tools:
        print("--- WARNING: No tools discovered from MCP server. Agent will lack database functionality. ---")
    
    agent_instance = Agent(
        name="async_database_agent",
        description="A database agent has access to a postgres database using an external MCP postgres tool.",
        model="gemini-2.5-flash-preview-04-17", # Ensure API key is in .env
        instruction="""
            You are a database analysis agent that helps users understand their database metrics and lead time for change data. You can:

            1. Connect to PostgreSQL databases
            2. Analyze lead time for change metrics
            3. Generate visualizations of the data
            4. Provide insights about the data

            When analyzing data, always:
            - Verify data completeness before analysis
            - Provide clear explanations of your findings
            - Use appropriate visualizations when needed
            - Handle errors gracefully

            For database queries:
            - Always use parameterized queries for security
            - Handle connection errors appropriately
            - Validate query results before processing

            For lead time analysis:
            - Calculate metrics accurately
            - Consider outliers and anomalies
            - Provide context for the numbers

            When generating visualizations:
            - Use appropriate chart types
            - Include clear labels and titles
            - Save plots in a consistent format

            Remember to:
            - Be thorough in your analysis
            - Provide actionable insights
            - Explain your methodology
            - Handle edge cases appropriately

            PostgreSQL Case Sensitivity Rules:
            - Table and column names containing uppercase letters MUST be quoted with double quotes
            - Example: Use "Commit" instead of commit, "prNumber" instead of prnumber
            - This is because PostgreSQL treats unquoted identifiers as lowercase by default
            - When identifiers are created with double quotes, they preserve their case sensitivity
            - Always use double quotes for:
            * Table names with uppercase letters (e.g., "Commit", "PullRequest")
            * Column names with uppercase letters (e.g., "prNumber", "estimatedOriginalTimestamp")
            - Example query:
            ```sql
            SELECT c."prNumber", c."estimatedOriginalTimestamp"
            FROM "Commit" c
            WHERE c."prNumber" = 35;
            ```
            ---
            IMPORTANT: If you see an error like 'column "prNumber" does not exist', it means the column name was not quoted and PostgreSQL is looking for a lowercase version. Always use double quotes for any table or column name that is not all-lowercase.

            Tip: To check the exact column names and their case, refer to the schema below or run this query in your database:
            SELECT column_name FROM information_schema.columns WHERE table_name = '<TableName>';

            If you get a column-not-found error, double-check your identifier quoting!

            ---
            Database Schema Reference (public schema):

            Table: "PullRequest"
            - "createdAt": timestamp without time zone
            - "number": integer
            - "mergedAt": timestamp without time zone
            - "baseBranch": text
            - "repo": text
            - "title": text
            - "author": text

            Table: "Commit"
            - "isMergeCommit": boolean
            - "createdAt": timestamp without time zone
            - "updatedAt": timestamp without time zone
            - "estimatedOriginalTimestamp": timestamp without time zone
            - "isSquashed": boolean
            - "authorDate": timestamp without time zone
            - "isRebased": boolean
            - "timestamp": timestamp without time zone
            - "prNumber": integer
            - "source": USER-DEFINED
            - "sha": text
            - "originalCommitSha": text
            - "message": text
            - "author": text
            - "repository": text
            - "id": text

            Table: "Metrics"
            - "developmentTimeHours": double precision
            - "prNumber": integer
            - "endToEndLtcHours": double precision
            - "nestedCommitCount": integer
            - "firstCommitTime": timestamp without time zone
            - "deployedAt": timestamp without time zone
            - "ltcHours": double precision
            - "createdAt": timestamp without time zone
            - "commitSha": text
            - "id": text

            ---
            Identifier Quoting Guidance:
            - Always use double quotes for table and column names with uppercase or camelCase letters.
            - Example: Use "prNumber" not prNumber, "Commit" not Commit.
            - Example query:
            SELECT * FROM "PullRequest" WHERE "number" = 3;
            SELECT c."sha", c."message", c."author", c."timestamp" FROM "Commit" c WHERE c."prNumber" = 35;

            Refer to this schema and quoting guidance for all queries to avoid errors.
            """,
        tools=tools,
    )
    
    return agent_instance, exit_stack

root_agent = create_agent()


