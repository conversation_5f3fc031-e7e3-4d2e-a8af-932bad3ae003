#!/usr/bin/env python3
"""
Test script to verify <PERSON> connections are working properly.
This script tests the Jenkins MCP server with the new environment variable configuration.
"""

import asyncio
import os
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

def test_environment_variables():
    """Test that all required environment variables are set"""
    print("=== Testing Environment Variables ===")
    
    required_vars = [
        "JENKINS_DEV_URL", "JENKINS_DEV_USERNAME", "J<PERSON><PERSON>INS_DEV_PASSWORD",
        "JENKINS_UAT_URL", "JENKINS_UAT_USERNAME", "JENKINS_UAT_PASSWORD", 
        "JENKINS_PROD_URL", "JEN<PERSON>INS_PROD_USERNAME", "J<PERSON><PERSON>INS_PROD_PASSWORD"
    ]
    
    missing_vars = []
    for var in required_vars:
        value = os.getenv(var)
        if value:
            print(f"✅ {var}: {'*' * len(value)}")  # Mask the actual values
        else:
            print(f"❌ {var}: Not set")
            missing_vars.append(var)
    
    if missing_vars:
        print(f"\n⚠️  Missing environment variables: {missing_vars}")
        return False
    else:
        print("\n✅ All environment variables are set!")
        return True

def test_jenkins_direct_connection():
    """Test direct connection to Jenkins instances"""
    print("\n=== Testing Direct Jenkins Connections ===")
    
    import jenkins
    
    jenkins_instances = [
        {
            "name": "Development Jenkins (qa-dev)",
            "url": os.getenv("JENKINS_DEV_URL"),
            "username": os.getenv("JENKINS_DEV_USERNAME"),
            "password": os.getenv("JENKINS_DEV_PASSWORD")
        },
        {
            "name": "UAT Jenkins (qa-sit)",
            "url": os.getenv("JENKINS_UAT_URL"),
            "username": os.getenv("JENKINS_UAT_USERNAME"),
            "password": os.getenv("JENKINS_UAT_PASSWORD")
        },
        {
            "name": "Production Jenkins (qa-stage, qa-prod)",
            "url": os.getenv("JENKINS_PROD_URL"),
            "username": os.getenv("JENKINS_PROD_USERNAME"),
            "password": os.getenv("JENKINS_PROD_PASSWORD")
        }
    ]
    
    successful_connections = 0
    
    for instance in jenkins_instances:
        if not instance["url"]:
            print(f"❌ {instance['name']}: No URL configured")
            continue
            
        try:
            print(f"🔄 Testing {instance['name']} at {instance['url']}...")
            client = jenkins.Jenkins(
                instance["url"], 
                username=instance["username"], 
                password=instance["password"],
                timeout=10
            )
            
            # Test connection by getting version
            version = client.get_version()
            print(f"✅ {instance['name']}: Connected successfully (version: {version})")
            
            # Get basic info
            jobs = client.get_jobs()
            print(f"   📊 Found {len(jobs)} jobs")
            
            successful_connections += 1
            
        except Exception as e:
            print(f"❌ {instance['name']}: Connection failed - {str(e)}")
    
    print(f"\n📈 Successfully connected to {successful_connections}/{len(jenkins_instances)} Jenkins instances")
    return successful_connections > 0

async def test_jenkins_mcp_server():
    """Test the Jenkins MCP server"""
    print("\n=== Testing Jenkins MCP Server ===")
    
    try:
        from agents.sequential_agent.jenkins_mcp_server.main import jenkins_lifespan, FastMCP
        
        # Create a mock FastMCP server for testing
        mock_server = FastMCP("test-jenkins-mcp")
        
        print("🔄 Testing Jenkins MCP server lifespan...")
        
        async with jenkins_lifespan(mock_server) as jenkins_ctx:
            print(f"✅ Jenkins MCP server started successfully!")
            print(f"   📊 Connected to {len(jenkins_ctx.clients)} Jenkins instances:")
            
            for instance_name, config in jenkins_ctx.instance_configs.items():
                print(f"   - {instance_name}: {config['url']} (environment: {config['environment']})")
            
            return True
            
    except Exception as e:
        print(f"❌ Jenkins MCP server test failed: {str(e)}")
        return False

def main():
    """Run all tests"""
    print("Jenkins Connection Test Suite")
    print("=" * 50)
    
    # Test 1: Environment variables
    env_test_passed = test_environment_variables()
    
    if not env_test_passed:
        print("\n❌ Environment variable test failed. Please check your .env file.")
        return
    
    # Test 2: Direct Jenkins connections
    direct_test_passed = test_jenkins_direct_connection()
    
    if not direct_test_passed:
        print("\n❌ Direct Jenkins connection test failed. Please check your Jenkins credentials and URLs.")
        return
    
    # Test 3: Jenkins MCP server
    print("\n🔄 Running Jenkins MCP server test...")
    try:
        mcp_test_passed = asyncio.run(test_jenkins_mcp_server())
        
        if mcp_test_passed:
            print("\n✅ All tests passed! Jenkins connections are working properly.")
            print("\n🎯 Environment Mapping Confirmed:")
            print("   - qa-dev → Development Jenkins (jenkins.truxt.ai)")
            print("   - qa-sit → UAT Jenkins (jenkins2.truxt.ai)")
            print("   - qa-stage → Production Jenkins (jenkins3.truxt.ai)")
            print("   - qa-prod → Production Jenkins (jenkins3.truxt.ai)")
        else:
            print("\n❌ Jenkins MCP server test failed.")
            
    except Exception as e:
        print(f"\n❌ Jenkins MCP server test failed with exception: {str(e)}")

if __name__ == "__main__":
    main()
