#!/usr/bin/env python3
"""
Example script demonstrating the enhanced logging and state management features
of the black-build-agents project.

This script shows how to:
1. Use agents with enhanced logging
2. Monitor agent state
3. Track execution metrics
4. Handle errors gracefully
"""

import asyncio
import json
from google.adk.runners import Runner
from google.adk.sessions import InMemorySessionService
from google.genai.types import Content, Part

# Import enhanced agents
from agents.database_agent.agent import create_agent as create_database_agent
from agents.github_agent.agent import create_agent as create_github_agent
from agents.sequential_agent.agent import create_sequential_agent


async def demonstrate_database_agent():
    """Demonstrate enhanced database agent with logging and state tracking"""
    print("\n=== Database Agent Demo ===")
    
    # Create the enhanced database agent
    agent, exit_stack = await create_database_agent()
    
    async with exit_stack:
        # Create session service and runner
        session_service = InMemorySessionService()
        runner = Runner(
            agent=agent,
            app_name="demo_app",
            session_service=session_service
        )
        
        # Create a session
        session = await session_service.create_session(
            app_name="demo_app",
            user_id="demo_user",
            session_id="demo_session_db"
        )
        
        print(f"Initial session state: {dict(session.state)}")
        
        # Run the agent with a sample query
        user_message = Content(parts=[Part(text="Show me the database schema and analyze recent metrics")])
        
        print("Running database agent...")
        events = runner.run(
            user_id="demo_user",
            session_id="demo_session_db",
            new_message=user_message
        )
        
        # Process events and show final response
        for event in events:
            if event.is_final_response():
                print(f"Agent Response: {event.content.parts[0].text[:200]}...")
        
        # Check updated session state
        updated_session = await session_service.get_session(
            app_name="demo_app",
            user_id="demo_user", 
            session_id="demo_session_db"
        )
        
        print(f"Final session state keys: {list(updated_session.state.keys())}")
        
        # Show agent-specific state
        if "db_agent:state" in updated_session.state:
            agent_state = updated_session.state["db_agent:state"]
            print(f"Database agent state: {json.dumps(agent_state, indent=2)}")


async def demonstrate_github_agent():
    """Demonstrate enhanced GitHub agent with logging and state tracking"""
    print("\n=== GitHub Agent Demo ===")
    
    # Create the enhanced GitHub agent
    agent, exit_stack = await create_github_agent()
    
    async with exit_stack:
        # Create session service and runner
        session_service = InMemorySessionService()
        runner = Runner(
            agent=agent,
            app_name="demo_app",
            session_service=session_service
        )
        
        # Create a session
        session = await session_service.create_session(
            app_name="demo_app",
            user_id="demo_user",
            session_id="demo_session_gh"
        )
        
        print(f"Initial session state: {dict(session.state)}")
        
        # Run the agent with a sample query
        user_message = Content(parts=[Part(text="Analyze PR #123 in repository owner/repo and track its commits across environments")])
        
        print("Running GitHub agent...")
        events = runner.run(
            user_id="demo_user",
            session_id="demo_session_gh",
            new_message=user_message
        )
        
        # Process events and show final response
        for event in events:
            if event.is_final_response():
                print(f"Agent Response: {event.content.parts[0].text[:200]}...")
        
        # Check updated session state
        updated_session = await session_service.get_session(
            app_name="demo_app",
            user_id="demo_user",
            session_id="demo_session_gh"
        )
        
        print(f"Final session state keys: {list(updated_session.state.keys())}")
        
        # Show agent-specific state
        if "github_agent:state" in updated_session.state:
            agent_state = updated_session.state["github_agent:state"]
            print(f"GitHub agent state: {json.dumps(agent_state, indent=2)}")


async def demonstrate_sequential_agent():
    """Demonstrate enhanced sequential agent with comprehensive pipeline tracking"""
    print("\n=== Sequential Agent Demo ===")
    
    # Create the enhanced sequential agent
    agent, exit_stack = await create_sequential_agent()
    
    async with exit_stack:
        # Create session service and runner
        session_service = InMemorySessionService()
        runner = Runner(
            agent=agent,
            app_name="demo_app",
            session_service=session_service
        )
        
        # Create a session
        session = await session_service.create_session(
            app_name="demo_app",
            user_id="demo_user",
            session_id="demo_session_seq"
        )
        
        print(f"Initial session state: {dict(session.state)}")
        
        # Run the agent with a sample DORA metrics request
        user_message = Content(parts=[Part(text="""
        Analyze DORA metrics for:
        - Repository: owner/repo
        - PR Number: 123
        
        Please trace the commits through all environments and calculate lead time metrics.
        """)])
        
        print("Running sequential agent pipeline...")
        events = runner.run(
            user_id="demo_user",
            session_id="demo_session_seq",
            new_message=user_message
        )
        
        # Process events and show final response
        for event in events:
            if event.is_final_response():
                print(f"Pipeline Response: {event.content.parts[0].text[:200]}...")
        
        # Check updated session state
        updated_session = await session_service.get_session(
            app_name="demo_app",
            user_id="demo_user",
            session_id="demo_session_seq"
        )
        
        print(f"Final session state keys: {list(updated_session.state.keys())}")
        
        # Show sequential agent state
        if "sequential_agent:state" in updated_session.state:
            agent_state = updated_session.state["sequential_agent:state"]
            print(f"Sequential agent state: {json.dumps(agent_state, indent=2)}")


async def demonstrate_state_persistence():
    """Demonstrate how state persists across multiple interactions"""
    print("\n=== State Persistence Demo ===")
    
    session_service = InMemorySessionService()
    
    # Create initial session with some state
    session = await session_service.create_session(
        app_name="demo_app",
        user_id="demo_user",
        session_id="persistence_demo",
        state={
            "user:analysis_preference": "detailed",
            "user:notification_enabled": True,
            "app:version": "1.0.0"
        }
    )
    
    print(f"Initial state: {dict(session.state)}")
    
    # Simulate agent updating state
    session.state["demo_agent:executions"] = 1
    session.state["demo_agent:last_run"] = "2024-01-15T10:30:00"
    
    # Retrieve session again to show persistence
    retrieved_session = await session_service.get_session(
        app_name="demo_app",
        user_id="demo_user",
        session_id="persistence_demo"
    )
    
    print(f"Retrieved state: {dict(retrieved_session.state)}")
    
    # Show different state scopes
    print("\nState by scope:")
    for key, value in retrieved_session.state.items():
        if key.startswith("user:"):
            print(f"  User state: {key} = {value}")
        elif key.startswith("app:"):
            print(f"  App state: {key} = {value}")
        elif ":" in key:
            print(f"  Agent state: {key} = {value}")
        else:
            print(f"  Session state: {key} = {value}")


def demonstrate_log_analysis():
    """Show how to analyze the generated log files"""
    print("\n=== Log Analysis Demo ===")
    
    import os
    import glob
    
    # Find all log files
    log_files = glob.glob("*.log")
    
    if not log_files:
        print("No log files found. Run the agents first to generate logs.")
        return
    
    print(f"Found log files: {log_files}")
    
    for log_file in log_files:
        if os.path.exists(log_file):
            print(f"\n--- {log_file} ---")
            
            # Read and analyze log file
            with open(log_file, 'r') as f:
                lines = f.readlines()
            
            # Count log levels
            levels = {"INFO": 0, "DEBUG": 0, "WARNING": 0, "ERROR": 0}
            for line in lines:
                for level in levels:
                    if f" - {level} - " in line:
                        levels[level] += 1
                        break
            
            print(f"Log level counts: {levels}")
            
            # Show recent entries
            print("Recent log entries:")
            for line in lines[-3:]:
                print(f"  {line.strip()}")


async def main():
    """Run all demonstrations"""
    print("Enhanced Logging and State Management Demo")
    print("=" * 50)
    
    try:
        # Demonstrate individual agents
        await demonstrate_database_agent()
        await demonstrate_github_agent()
        await demonstrate_sequential_agent()
        
        # Demonstrate state features
        await demonstrate_state_persistence()
        
        # Analyze generated logs
        demonstrate_log_analysis()
        
    except Exception as e:
        print(f"Demo error: {e}")
        import traceback
        traceback.print_exc()
    
    print("\n" + "=" * 50)
    print("Demo completed! Check the generated log files for detailed execution traces.")
    print("Log files: database_agent.log, github_agent.log, sequential_agent.log")


if __name__ == "__main__":
    asyncio.run(main())
