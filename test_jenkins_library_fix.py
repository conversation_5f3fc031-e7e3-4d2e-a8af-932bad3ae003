#!/usr/bin/env python3
"""
Test different approaches to fix the python-jenkins library SSL issue.
"""

import jenkins
import requests
import urllib3
import os
from dotenv import load_dotenv

# Disable SSL warnings
urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)

# Load environment variables
load_dotenv()

def test_approach_1():
    """Test approach 1: Monkey patch after creation"""
    print("=== Approach 1: Monkey patch after creation ===")
    
    try:
        client = jenkins.<PERSON>("https://jenkins.truxt.ai/", username="admin", password="Truxt@2025", timeout=30)
        
        # Create custom session
        session = requests.Session()
        session.verify = False
        
        # Monkey patch
        client._session = session
        
        version = client.get_version()
        print(f"✅ Approach 1 worked! Version: {version}")
        return True
        
    except Exception as e:
        print(f"❌ Approach 1 failed: {str(e)}")
        return False

def test_approach_2():
    """Test approach 2: Patch requests globally"""
    print("\n=== Approach 2: Patch requests globally ===")
    
    try:
        # Patch requests globally
        original_request = requests.Session.request
        
        def patched_request(self, method, url, **kwargs):
            kwargs['verify'] = False
            return original_request(self, method, url, **kwargs)
        
        requests.Session.request = patched_request
        
        client = jenkins.Jenkins("https://jenkins.truxt.ai/", username="admin", password="Truxt@2025", timeout=30)
        version = client.get_version()
        
        # Restore original
        requests.Session.request = original_request
        
        print(f"✅ Approach 2 worked! Version: {version}")
        return True
        
    except Exception as e:
        print(f"❌ Approach 2 failed: {str(e)}")
        # Restore original just in case
        try:
            requests.Session.request = original_request
        except:
            pass
        return False

def test_approach_3():
    """Test approach 3: Custom Jenkins class"""
    print("\n=== Approach 3: Custom Jenkins class ===")
    
    try:
        class CustomJenkins(jenkins.Jenkins):
            def __init__(self, url, username=None, password=None, timeout=None):
                super().__init__(url, username, password, timeout)
                # Override the session with SSL disabled
                self._session = requests.Session()
                self._session.auth = (username, password) if username and password else None
                self._session.verify = False
                self._session.timeout = timeout
        
        client = CustomJenkins("https://jenkins.truxt.ai/", username="admin", password="Truxt@2025", timeout=30)
        version = client.get_version()
        print(f"✅ Approach 3 worked! Version: {version}")
        return True
        
    except Exception as e:
        print(f"❌ Approach 3 failed: {str(e)}")
        return False

def test_approach_4():
    """Test approach 4: Environment variable for SSL"""
    print("\n=== Approach 4: Environment variable for SSL ===")
    
    try:
        # Set environment variables to disable SSL
        os.environ['PYTHONHTTPSVERIFY'] = '0'
        os.environ['CURL_CA_BUNDLE'] = ''
        os.environ['REQUESTS_CA_BUNDLE'] = ''
        
        client = jenkins.Jenkins("https://jenkins.truxt.ai/", username="admin", password="Truxt@2025", timeout=30)
        version = client.get_version()
        print(f"✅ Approach 4 worked! Version: {version}")
        return True
        
    except Exception as e:
        print(f"❌ Approach 4 failed: {str(e)}")
        return False

def test_approach_5():
    """Test approach 5: Use HTTP instead of HTTPS"""
    print("\n=== Approach 5: Use HTTP instead of HTTPS ===")
    
    try:
        # Test if HTTP works (it should redirect to HTTPS but maybe jenkins library handles it)
        client = jenkins.Jenkins("http://jenkins.truxt.ai/", username="admin", password="Truxt@2025", timeout=30)
        version = client.get_version()
        print(f"✅ Approach 5 worked! Version: {version}")
        return True
        
    except Exception as e:
        print(f"❌ Approach 5 failed: {str(e)}")
        return False

if __name__ == "__main__":
    print("Testing different approaches to fix python-jenkins SSL issue")
    print("=" * 60)
    
    approaches = [
        test_approach_1,
        test_approach_2, 
        test_approach_3,
        test_approach_4,
        test_approach_5
    ]
    
    working_approaches = []
    
    for approach in approaches:
        if approach():
            working_approaches.append(approach.__name__)
    
    print(f"\n📊 Summary:")
    print(f"Working approaches: {working_approaches}")
    
    if working_approaches:
        print(f"✅ Found {len(working_approaches)} working solution(s)!")
    else:
        print("❌ No approaches worked. The issue might be deeper in the library.")
