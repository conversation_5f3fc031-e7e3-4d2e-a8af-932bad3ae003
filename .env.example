# Choose ONE of the following authentication methods:

# Option A: Vertex AI (Recommended for Google Cloud integration)
# GOOGLE_GENAI_USE_VERTEXAI="True"
# GOOGLE_CLOUD_PROJECT="your-gcp-project-id"
# GOOGLE_CLOUD_LOCATION="us-central1" # e.g., us-central1

# Option B: Google AI Studio API Key (Simpler for quick tests)
GOOGLE_API_KEY="your-google-ai-studio-api-key"

# GitHub Configuration
GITHUB_PERSONAL_ACCESS_TOKEN="your-github-token"

# Database Configuration
DATABASE_URL="postgresql://username:password@host:port/database"

# Multiple Jenkins Instances Configuration
# Development Environment (qa-dev)
JENKINS_DEV_URL="https://jenkins.truxt.ai/"
JENKINS_DEV_USERNAME="admin"
JENKINS_DEV_PASSWORD="Truxt@2025"

# SIT Environment (qa-sit)
JENKINS_UAT_URL="https://jenkins2.truxt.ai/"
JENKINS_UAT_USERNAME="admin"
JENKINS_UAT_PASSWORD="Truxt@2025"

# Stage and Production Environment (qa-stage, qa-prod)
JENKINS_PROD_URL="https://jenkins3.truxt.ai/"
JENKINS_PROD_USERNAME="admin"
JENKINS_PROD_PASSWORD="Truxt@2025"

# Legacy Jenkins Configuration (for backward compatibility)
JENKINS_URL="https://jenkins.truxt.ai/"
JENKINS_USERNAME="admin"
JENKINS_API_TOKEN="Truxt@2025"

# JFrog Artifactory Configuration
JFROG_URL="https://trialiozmhb.jfrog.io/"
JFROG_USERNAME="kartikeya"
JFROG_PASSWORD="Truxt@2025"
JFROG_ACCESS_TOKEN="****************************************************************"

# JFrog Artifact Paths by Environment
JFROG_DEV_PATH="truxt12345678-repo-1/nextjs-builds/"
JFROG_SIT_PATH="sitCI/nextjs-builds/"
JFROG_STAGE_PATH="stageCI/nextjs-builds/"
JFROG_PROD_PATH="prodCI/nextjs-builds/"

# Data Ingestion Endpoint
INGEST_ENDPOINT="http://52.233.88.40:3000/ingest"

# DORA Metrics Configuration
DORA_ENVIRONMENTS="qa-dev,qa-sit,qa-stage,qa-prod"
DORA_ENVIRONMENT_MAPPING="qa-dev:dev,qa-sit:sit,qa-stage:stage,qa-prod:prod"