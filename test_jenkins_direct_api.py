#!/usr/bin/env python3
"""
Test Jenkins API directly using requests library to bypass python-jenkins issues.
"""

import requests
import json
import os
from dotenv import load_dotenv
import urllib3

# Disable SSL warnings
urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)

# Load environment variables
load_dotenv()

class DirectJenkinsClient:
    """Direct Jenkins API client using requests"""
    
    def __init__(self, url, username, password):
        self.url = url.rstrip('/')
        self.username = username
        self.password = password
        self.session = requests.Session()
        self.session.auth = (username, password)
        self.session.verify = False  # Disable SSL verification
        self.session.timeout = 30
    
    def get_version(self):
        """Get Jenkins version"""
        response = self.session.get(f"{self.url}/api/json")
        response.raise_for_status()
        # Version is usually in headers
        return response.headers.get('x-jenkins', 'unknown')
    
    def get_jobs(self):
        """Get list of jobs"""
        response = self.session.get(f"{self.url}/api/json?tree=jobs[name,url]")
        response.raise_for_status()
        data = response.json()
        return data.get('jobs', [])
    
    def search_builds_by_commit(self, commit_hash, job_name=None):
        """Search for builds containing a specific commit hash"""
        jobs = self.get_jobs() if not job_name else [{'name': job_name}]
        
        results = []
        for job in jobs[:5]:  # Limit to first 5 jobs for testing
            job_name = job['name']
            try:
                # Get recent builds for this job
                response = self.session.get(
                    f"{self.url}/job/{job_name}/api/json?tree=builds[number,url,timestamp,duration,result]{{0,10}}"
                )
                if response.status_code == 200:
                    job_data = response.json()
                    builds = job_data.get('builds', [])
                    
                    for build in builds[:3]:  # Check last 3 builds
                        build_number = build['number']
                        
                        # Get build details
                        build_response = self.session.get(
                            f"{self.url}/job/{job_name}/{build_number}/api/json"
                        )
                        if build_response.status_code == 200:
                            build_data = build_response.json()
                            
                            # Check if this build contains our commit
                            if self._build_contains_commit(build_data, commit_hash):
                                results.append({
                                    'job_name': job_name,
                                    'build_number': build_number,
                                    'url': build['url'],
                                    'timestamp': build['timestamp'],
                                    'duration': build['duration'],
                                    'result': build['result']
                                })
                                
            except Exception as e:
                print(f"   Error checking job {job_name}: {str(e)}")
                continue
        
        return results
    
    def _build_contains_commit(self, build_data, commit_hash):
        """Check if build contains the specified commit hash"""
        # Check in various places where commit info might be stored
        actions = build_data.get('actions', [])
        
        for action in actions:
            # Check in lastBuiltRevision
            if 'lastBuiltRevision' in action:
                if action['lastBuiltRevision'].get('SHA1') == commit_hash:
                    return True
                    
                # Check in branch info
                for branch in action['lastBuiltRevision'].get('branch', []):
                    if branch.get('SHA1') == commit_hash:
                        return True
            
            # Check in buildsByBranchName
            for branch_info in action.get('buildsByBranchName', {}).values():
                if branch_info.get('marked', {}).get('SHA1') == commit_hash:
                    return True
                if branch_info.get('revision', {}).get('SHA1') == commit_hash:
                    return True
        
        return False

def test_direct_jenkins_api():
    """Test Jenkins API using direct requests"""
    print("=== Testing Direct Jenkins API ===")
    
    jenkins_instances = [
        {
            "name": "Development Jenkins (qa-dev)",
            "url": "https://jenkins.truxt.ai/",
            "username": "admin",
            "password": "Truxt@2025"
        },
        {
            "name": "UAT Jenkins (qa-sit)",
            "url": "https://jenkins2.truxt.ai/",
            "username": "admin",
            "password": "Truxt@2025"
        },
        {
            "name": "Production Jenkins (qa-stage, qa-prod)",
            "url": "https://jenkins3.truxt.ai/",
            "username": "admin",
            "password": "Truxt@2025"
        }
    ]
    
    successful_connections = 0
    
    for instance in jenkins_instances:
        print(f"🔄 Testing {instance['name']} at {instance['url']}...")
        
        try:
            client = DirectJenkinsClient(
                instance["url"],
                instance["username"],
                instance["password"]
            )
            
            # Test connection
            version = client.get_version()
            print(f"✅ {instance['name']}: Connected successfully (version: {version})")
            
            # Get jobs
            jobs = client.get_jobs()
            print(f"   📊 Found {len(jobs)} jobs")
            
            if jobs:
                print(f"   📝 Sample jobs: {[job['name'] for job in jobs[:3]]}")
                
                # Test searching for a commit (using a dummy commit hash)
                test_commit = "abc123def456"  # Dummy commit hash
                print(f"   🔍 Testing commit search for: {test_commit}")
                builds = client.search_builds_by_commit(test_commit)
                print(f"   📋 Found {len(builds)} builds with commit (expected: 0 for dummy hash)")
            
            successful_connections += 1
            
        except Exception as e:
            print(f"❌ {instance['name']}: Connection failed - {str(e)}")
    
    print(f"\n📈 Successfully connected to {successful_connections}/{len(jenkins_instances)} Jenkins instances")
    
    if successful_connections > 0:
        print("\n✅ Direct Jenkins API connections are working!")
        print("💡 We can use direct API calls instead of python-jenkins library")
        return True
    else:
        print("\n❌ No Jenkins connections successful")
        return False

if __name__ == "__main__":
    print("Jenkins Direct API Test")
    print("=" * 40)
    test_direct_jenkins_api()
