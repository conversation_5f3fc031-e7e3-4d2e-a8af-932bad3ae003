#!/usr/bin/env python3
"""
Quick test to verify Jenkins HTTPS connections work with authentication.
"""

import jenkins
import os
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

def test_jenkins_https():
    """Test Jenkins connections with HTTPS URLs"""
    
    # Test URLs with HTTPS
    jenkins_instances = [
        {
            "name": "<PERSON> Jenkins (qa-dev)",
            "url": "https://jenkins.truxt.ai/",
            "username": os.getenv("JENKINS_DEV_USERNAME", "admin"),
            "password": os.getenv("JENKINS_DEV_PASSWORD", "Truxt@2025")
        },
        {
            "name": "<PERSON><PERSON> (qa-sit)",
            "url": "https://jenkins2.truxt.ai/",
            "username": os.getenv("JENKINS_UAT_USERNAME", "admin"),
            "password": os.getenv("JENKINS_UAT_PASSWORD", "Truxt@2025")
        },
        {
            "name": "<PERSON> (qa-stage, qa-prod)",
            "url": "https://jenkins3.truxt.ai/",
            "username": os.getenv("JENKINS_PROD_USERNAME", "admin"),
            "password": os.getenv("JENKINS_PROD_PASSWORD", "Truxt@2025")
        }
    ]
    
    successful_connections = 0
    
    for instance in jenkins_instances:
        print(f"🔄 Testing {instance['name']} at {instance['url']}...")
        
        try:
            # Create Jenkins client
            client = jenkins.Jenkins(
                instance["url"], 
                username=instance["username"], 
                password=instance["password"],
                timeout=30
            )
            
            # Test connection by getting version
            version = client.get_version()
            print(f"✅ {instance['name']}: Connected successfully (version: {version})")
            
            # Get basic info
            try:
                jobs = client.get_jobs()
                print(f"   📊 Found {len(jobs)} jobs")
                
                # List first few jobs
                if jobs:
                    print(f"   📝 Sample jobs: {[job['name'] for job in jobs[:3]]}")
                    
            except Exception as job_e:
                print(f"   ⚠️  Could not list jobs: {str(job_e)}")
            
            successful_connections += 1
            
        except Exception as e:
            print(f"❌ {instance['name']}: Connection failed - {str(e)}")
            
            # Try to provide more specific error information
            if "401" in str(e) or "Unauthorized" in str(e):
                print(f"   🔍 Authentication failed - check username/password")
            elif "403" in str(e) or "Forbidden" in str(e):
                print(f"   🔍 Access forbidden - check user permissions")
            elif "timeout" in str(e).lower():
                print(f"   🔍 Connection timeout - check network connectivity")
            else:
                print(f"   🔍 General connection error - check URL and network")
    
    print(f"\n📈 Successfully connected to {successful_connections}/{len(jenkins_instances)} Jenkins instances")
    
    if successful_connections > 0:
        print("\n✅ Jenkins connections are working!")
        print("\n🎯 Environment Mapping:")
        print("   - qa-dev → Development Jenkins (jenkins.truxt.ai)")
        print("   - qa-sit → UAT Jenkins (jenkins2.truxt.ai)")
        print("   - qa-stage → Production Jenkins (jenkins3.truxt.ai)")
        print("   - qa-prod → Production Jenkins (jenkins3.truxt.ai)")
        return True
    else:
        print("\n❌ No Jenkins connections successful")
        return False

if __name__ == "__main__":
    print("Jenkins HTTPS Connection Test")
    print("=" * 40)
    test_jenkins_https()
