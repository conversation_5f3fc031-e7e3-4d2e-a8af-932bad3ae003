# DORA Metrics Agent

This project demonstrates an agent system built using the Google Agent Development Kit (ADK) to calculate and monitor DORA (DevOps Research and Assessment) metrics for software development teams.

## Overview

DORA Metrics are key performance indicators for DevOps teams, including:

- **Deployment Frequency**: How often code is deployed to production
- **Lead Time for Changes**: Time from code commit to production deployment
- **Mean Time to Recovery (MTTR)**: Time to restore service after an incident
- **Change Failure Rate**: Percentage of deployments causing a failure in production

This agent system uses the Model Context Protocol (MCP) to integrate with development tools and automatically calculate these metrics.

## Agents

- **Coordinator Agent**: Orchestrates the sub-agents and provides an interface for users
- **GitHub Agent**: Extracts data from GitHub repositories (commits, pull requests, etc.)
- **Jenkins Agent**: Extracts data from Jenkins pipelines (build and deployment data)
- **Metrics Calculator Agent**: Processes raw data to calculate DORA metrics
- **Visualizer Agent**: Generates visualizations of DORA metrics over time

## Setup

1. **Clone the repository:**

   ```bash
   git clone <repository-url>
   cd dora-metrics-agent
   ```

2. **Create and activate a virtual environment:**

   ```bash
   python -m venv .venv
   # On Windows
   .\.venv\Scripts\activate
   # On macOS/Linux
   source .venv/bin/activate
   ```

3. **Install dependencies:**

   ```bash
   pip install -r requirements.txt
   ```

4. **Set up environment variables:**

   Create a `.env` file in the root directory with the following variables:

   ```
   GOOGLE_API_KEY=your_google_api_key
   GITHUB_TOKEN=your_github_token
   JENKINS_URL=your_jenkins_url
   JENKINS_USERNAME=your_jenkins_username
   JENKINS_API_TOKEN=your_jenkins_api_token
   ```

## Running the Agents

1. **Start the Coordinator Agent:**

   ```bash
   adk run agents/coordinator
   ```

2. **Or run individual agents:**

   ```bash
   adk run agents/github_agent
   adk run agents/jenkins_agent
   adk run agents/metrics_calculator
   ```

3. **Use the web interface (recommended for visualization):**

   ```bash
   adk web
   ```

## Project Structure

```
dora-metrics-agent/
├── agents/
│   ├── coordinator/           # Main coordinator agent
│   │   ├── __init__.py
│   │   └── agent.py
│   ├── github_agent/          # Agent for GitHub integration
│   │   ├── __init__.py
│   │   └── agent.py
│   ├── jenkins_agent/         # Agent for Jenkins integration
│   │   ├── __init__.py
│   │   └── agent.py
│   ├── metrics_calculator/    # Agent for processing DORA metrics
│   │   ├── __init__.py
│   │   └── agent.py
│   ├── visualizer/            # Agent for data visualization
│   │   ├── __init__.py
│   │   └── agent.py
│   └── __init__.py
├── utils/                     # Shared utility functions
│   ├── __init__.py
│   ├── github_utils.py
│   ├── jenkins_utils.py
│   └── metrics_utils.py
├── tests/                     # Unit and integration tests
│   ├── __init__.py
│   └── test_metrics.py
├── .env.example               # Example environment variables file
├── .gitignore                 # Git ignore file
├── requirements.txt           # Project dependencies
└── README.md                  # Project documentation
```

## Using MCP with the Agent System

This project utilizes the Model Context Protocol (MCP) to facilitate communication between agents and external systems. MCP allows for:

- Standard integration with code repositories
- Consistent API for accessing CI/CD platforms
- Secure credential management
- Real-time data access and processing

## Contributing

Contributions are welcome! Please feel free to submit a Pull Request.
