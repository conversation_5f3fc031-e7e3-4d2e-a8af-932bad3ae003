#!/usr/bin/env python3
"""
Debug Jenkins connection issues with detailed error reporting.
"""

import requests
import jenkins
import os
import urllib3
from dotenv import load_dotenv

# Disable SSL warnings for testing
urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)

# Load environment variables
load_dotenv()

def test_raw_http_connection():
    """Test raw HTTP connections to Jenkins instances"""
    print("=== Testing Raw HTTP Connections ===")
    
    urls = [
        "https://jenkins.truxt.ai/",
        "https://jenkins2.truxt.ai/",
        "https://jenkins3.truxt.ai/"
    ]
    
    for url in urls:
        print(f"🔄 Testing raw HTTP connection to {url}...")
        
        try:
            # Test with requests library
            response = requests.get(url, timeout=10, verify=False)
            print(f"✅ {url}: HTTP {response.status_code} - {response.reason}")
            
            # Check if it's a Jenkins server
            if 'x-jenkins' in response.headers:
                jenkins_version = response.headers.get('x-jenkins', 'unknown')
                print(f"   🎯 <PERSON> version: {jenkins_version}")
            else:
                print(f"   ⚠️  No Jenkins headers found")
                
        except Exception as e:
            print(f"❌ {url}: Raw HTTP failed - {str(e)}")

def test_jenkins_with_ssl_disabled():
    """Test Jenkins connections with SSL verification disabled"""
    print("\n=== Testing Jenkins Connections (SSL Disabled) ===")
    
    jenkins_instances = [
        {
            "name": "Development Jenkins (qa-dev)",
            "url": "https://jenkins.truxt.ai/",
            "username": "admin",
            "password": "Truxt@2025"
        },
        {
            "name": "UAT Jenkins (qa-sit)",
            "url": "https://jenkins2.truxt.ai/",
            "username": "admin", 
            "password": "Truxt@2025"
        },
        {
            "name": "Production Jenkins (qa-stage, qa-prod)",
            "url": "https://jenkins3.truxt.ai/",
            "username": "admin",
            "password": "Truxt@2025"
        }
    ]
    
    successful_connections = 0
    
    for instance in jenkins_instances:
        print(f"🔄 Testing {instance['name']} at {instance['url']}...")
        
        try:
            # Create Jenkins client with SSL verification disabled
            import ssl
            ssl_context = ssl.create_default_context()
            ssl_context.check_hostname = False
            ssl_context.verify_mode = ssl.CERT_NONE
            
            # Try with different approaches
            approaches = [
                {"name": "Standard", "extra_params": {}},
                {"name": "SSL Disabled", "extra_params": {"verify": False}},
            ]
            
            for approach in approaches:
                try:
                    print(f"   Trying {approach['name']} approach...")
                    
                    # Create custom session with SSL disabled
                    session = requests.Session()
                    session.verify = False
                    
                    client = jenkins.Jenkins(
                        instance["url"], 
                        username=instance["username"], 
                        password=instance["password"],
                        timeout=30
                    )
                    
                    # Monkey patch the session to disable SSL
                    client._session = session
                    
                    # Test connection
                    version = client.get_version()
                    print(f"✅ {instance['name']}: Connected successfully (version: {version})")
                    
                    # Get basic info
                    jobs = client.get_jobs()
                    print(f"   📊 Found {len(jobs)} jobs")
                    
                    successful_connections += 1
                    break  # Success, no need to try other approaches
                    
                except Exception as approach_e:
                    print(f"   ❌ {approach['name']} failed: {str(approach_e)}")
                    continue
            
        except Exception as e:
            print(f"❌ {instance['name']}: All approaches failed - {str(e)}")
    
    print(f"\n📈 Successfully connected to {successful_connections}/{len(jenkins_instances)} Jenkins instances")
    return successful_connections > 0

def test_authentication_methods():
    """Test different authentication methods"""
    print("\n=== Testing Authentication Methods ===")
    
    test_url = "https://jenkins.truxt.ai/"
    username = "admin"
    password = "Truxt@2025"
    
    print(f"🔄 Testing authentication methods for {test_url}...")
    
    # Test 1: Basic Auth with requests
    try:
        print("   Testing basic auth with requests...")
        response = requests.get(
            f"{test_url}api/json",
            auth=(username, password),
            timeout=10,
            verify=False
        )
        print(f"   ✅ Basic auth: HTTP {response.status_code}")
        if response.status_code == 200:
            print(f"   📊 Response length: {len(response.text)} chars")
    except Exception as e:
        print(f"   ❌ Basic auth failed: {str(e)}")
    
    # Test 2: Jenkins library with different parameters
    try:
        print("   Testing Jenkins library...")
        client = jenkins.Jenkins(test_url, username=username, password=password, timeout=30)
        
        # Disable SSL verification
        client._session.verify = False
        
        version = client.get_version()
        print(f"   ✅ Jenkins library: Connected (version: {version})")
    except Exception as e:
        print(f"   ❌ Jenkins library failed: {str(e)}")

if __name__ == "__main__":
    print("Jenkins Connection Debug Tool")
    print("=" * 50)
    
    test_raw_http_connection()
    test_jenkins_with_ssl_disabled()
    test_authentication_methods()
